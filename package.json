{"name": "hospital-management-system", "version": "1.0.0", "description": "Hospital Management System with Next.js Frontend and Microservices Backend", "private": true, "workspaces": ["frontend", "backend", "backend/api-gateway", "backend/services/*", "backend/shared"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:full": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm run start", "start:backend": "cd backend && npm run start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm run install:all", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf node_modules .next", "clean:backend": "cd backend && npm run clean", "docker:dev": "cd backend && npm run docker:dev", "docker:prod": "cd backend && npm run docker:prod", "docker:down": "cd backend && npm run docker:down", "docker:clean": "cd backend && npm run docker:clean", "health-check": "cd backend && npm run health-check", "logs": "cd backend && npm run logs", "microservices:start": "node -e \"require('child_process').spawn('powershell', ['-File', './scripts/start-microservices.ps1'], {stdio: 'inherit'})\"", "microservices:start:bash": "bash ./scripts/start-microservices.sh", "dev:microservices": "concurrently \"npm run microservices:start\" \"npm run dev:frontend\""}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/Kutou01/hospital-management.git"}, "keywords": ["hospital", "management", "healthcare", "nextjs", "microservices", "nodejs", "typescript", "react", "supabase"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.49.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "lucide-react": "^0.511.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}}