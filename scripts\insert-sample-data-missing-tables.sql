-- =====================================================
-- INSERT SAMPLE DATA FOR MISSING TABLES
-- =====================================================
-- This script inserts sample data for: billing, payments, schedules, availability

-- =====================================================
-- 1. INSERT SAMPLE SCHEDULES (Doctor Weekly Schedules)
-- =====================================================

-- Get existing doctor IDs first
DO $$
DECLARE
    doctor_rec RECORD;
BEGIN
    -- Insert schedules for each existing doctor
    FOR doctor_rec IN SELECT doctor_id FROM doctors LOOP
        -- Monday to Friday schedule (8:00 AM - 5:00 PM)
        INSERT INTO schedules (
            schedule_id, 
            doctor_id, 
            day_of_week, 
            start_time, 
            end_time, 
            break_start_time, 
            break_end_time,
            max_appointments,
            appointment_duration,
            notes,
            created_by
        ) VALUES 
        -- Monday
        (generate_schedule_id(), doctor_rec.doctor_id, 1, '08:00', '17:00', '12:00', '13:00', 8, 30, 'Regular working hours', 
         (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)),
        -- Tuesday  
        (generate_schedule_id(), doctor_rec.doctor_id, 2, '08:00', '17:00', '12:00', '13:00', 8, 30, 'Regular working hours',
         (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)),
        -- Wednesday
        (generate_schedule_id(), doctor_rec.doctor_id, 3, '08:00', '17:00', '12:00', '13:00', 8, 30, 'Regular working hours',
         (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)),
        -- Thursday
        (generate_schedule_id(), doctor_rec.doctor_id, 4, '08:00', '17:00', '12:00', '13:00', 8, 30, 'Regular working hours',
         (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)),
        -- Friday
        (generate_schedule_id(), doctor_rec.doctor_id, 5, '08:00', '17:00', '12:00', '13:00', 8, 30, 'Regular working hours',
         (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)),
        -- Saturday (Half day)
        (generate_schedule_id(), doctor_rec.doctor_id, 6, '08:00', '12:00', NULL, NULL, 4, 30, 'Half day schedule',
         (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1))
        ON CONFLICT (doctor_id, day_of_week) DO NOTHING;
    END LOOP;
    
    RAISE NOTICE '✅ Sample schedules inserted for all doctors';
END $$;

-- =====================================================
-- 2. INSERT SAMPLE AVAILABILITY (Next 30 days)
-- =====================================================

DO $$
DECLARE
    doctor_rec RECORD;
    date_rec DATE;
    day_of_week INTEGER;
    schedule_rec RECORD;
BEGIN
    -- Insert availability for each doctor for the next 30 days
    FOR doctor_rec IN SELECT doctor_id FROM doctors LOOP
        FOR i IN 0..29 LOOP
            date_rec := CURRENT_DATE + i;
            day_of_week := EXTRACT(DOW FROM date_rec); -- 0=Sunday, 6=Saturday
            
            -- Get schedule for this day of week
            SELECT * INTO schedule_rec 
            FROM schedules 
            WHERE doctor_id = doctor_rec.doctor_id 
            AND day_of_week = EXTRACT(DOW FROM date_rec)
            AND is_active = true
            LIMIT 1;
            
            -- Insert availability if schedule exists
            IF FOUND THEN
                INSERT INTO availability (
                    availability_id,
                    doctor_id,
                    date,
                    start_time,
                    end_time,
                    is_available,
                    max_appointments,
                    appointment_duration,
                    notes,
                    created_by
                ) VALUES (
                    generate_availability_id(),
                    doctor_rec.doctor_id,
                    date_rec,
                    schedule_rec.start_time,
                    schedule_rec.end_time,
                    true,
                    schedule_rec.max_appointments,
                    schedule_rec.appointment_duration,
                    'Auto-generated from schedule',
                    (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)
                ) ON CONFLICT (doctor_id, date) DO NOTHING;
            END IF;
        END LOOP;
    END LOOP;
    
    RAISE NOTICE '✅ Sample availability inserted for next 30 days';
END $$;

-- =====================================================
-- 3. INSERT SAMPLE BILLING DATA
-- =====================================================

-- Insert sample bills for existing appointments
DO $$
DECLARE
    apt_rec RECORD;
    bill_id_var VARCHAR(20);
    item_id_var VARCHAR(20);
    consultation_fee DECIMAL(10,2) := 200000; -- 200,000 VND
    tax_rate DECIMAL(4,2) := 0.10; -- 10% tax
BEGIN
    -- Create bills for completed appointments
    FOR apt_rec IN 
        SELECT a.appointment_id, a.patient_id, a.doctor_id, a.appointment_datetime,
               d.consultation_fee
        FROM appointments a
        JOIN doctors d ON a.doctor_id = d.doctor_id
        WHERE a.status = 'completed'
        LIMIT 5
    LOOP
        bill_id_var := generate_bill_id();
        
        -- Insert bill
        INSERT INTO billing (
            bill_id,
            patient_id,
            appointment_id,
            invoice_number,
            bill_date,
            due_date,
            status,
            subtotal,
            tax_amount,
            discount_amount,
            insurance_coverage,
            total_amount,
            notes,
            created_by
        ) VALUES (
            bill_id_var,
            apt_rec.patient_id,
            apt_rec.appointment_id,
            'INV-' || TO_CHAR(apt_rec.appointment_datetime, 'YYYYMMDD') || '-' || SUBSTRING(bill_id_var, 5),
            apt_rec.appointment_datetime,
            apt_rec.appointment_datetime + INTERVAL '30 days',
            'paid',
            COALESCE(apt_rec.consultation_fee, consultation_fee),
            ROUND(COALESCE(apt_rec.consultation_fee, consultation_fee) * tax_rate, 2),
            0,
            0,
            ROUND(COALESCE(apt_rec.consultation_fee, consultation_fee) * (1 + tax_rate), 2),
            'Consultation fee for appointment ' || apt_rec.appointment_id,
            (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)
        );
        
        -- Insert bill item for consultation
        item_id_var := generate_bill_item_id();
        INSERT INTO bill_items (
            item_id,
            bill_id,
            service_type,
            service_id,
            description,
            quantity,
            unit_price,
            total_price
        ) VALUES (
            item_id_var,
            bill_id_var,
            'consultation',
            apt_rec.doctor_id,
            'Medical consultation',
            1,
            COALESCE(apt_rec.consultation_fee, consultation_fee),
            COALESCE(apt_rec.consultation_fee, consultation_fee)
        );
        
        -- Insert payment for this bill
        INSERT INTO payments (
            payment_id,
            bill_id,
            payment_method_id,
            amount,
            payment_date,
            transaction_id,
            status,
            notes,
            processed_by
        ) VALUES (
            generate_payment_id(),
            bill_id_var,
            (SELECT id FROM payment_methods WHERE code = 'cash' LIMIT 1),
            ROUND(COALESCE(apt_rec.consultation_fee, consultation_fee) * (1 + tax_rate), 2),
            apt_rec.appointment_datetime + INTERVAL '1 hour',
            'TXN-' || EXTRACT(EPOCH FROM NOW())::BIGINT,
            'completed',
            'Cash payment for consultation',
            (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)
        );
        
    END LOOP;
    
    RAISE NOTICE '✅ Sample billing data inserted';
END $$;

-- =====================================================
-- 4. INSERT ADDITIONAL SAMPLE BILLS (Pending)
-- =====================================================

DO $$
DECLARE
    patient_rec RECORD;
    bill_id_var VARCHAR(20);
    item_id_var VARCHAR(20);
    lab_fee DECIMAL(10,2) := 150000; -- 150,000 VND
    medication_fee DECIMAL(10,2) := 80000; -- 80,000 VND
    total_amount DECIMAL(10,2);
BEGIN
    -- Create pending bills for existing patients
    FOR patient_rec IN 
        SELECT patient_id FROM patients LIMIT 3
    LOOP
        bill_id_var := generate_bill_id();
        total_amount := lab_fee + medication_fee;
        
        -- Insert pending bill
        INSERT INTO billing (
            bill_id,
            patient_id,
            appointment_id,
            invoice_number,
            bill_date,
            due_date,
            status,
            subtotal,
            tax_amount,
            discount_amount,
            insurance_coverage,
            total_amount,
            notes,
            created_by
        ) VALUES (
            bill_id_var,
            patient_rec.patient_id,
            NULL,
            'INV-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || SUBSTRING(bill_id_var, 5),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP + INTERVAL '15 days',
            'pending',
            total_amount,
            ROUND(total_amount * 0.10, 2),
            0,
            ROUND(total_amount * 0.30, 2), -- 30% insurance coverage
            ROUND(total_amount * 0.80, 2), -- Total after insurance
            'Lab tests and medication',
            (SELECT id FROM profiles WHERE role = 'admin' LIMIT 1)
        );
        
        -- Insert bill items
        INSERT INTO bill_items (
            item_id,
            bill_id,
            service_type,
            service_id,
            description,
            quantity,
            unit_price,
            total_price
        ) VALUES 
        (
            generate_bill_item_id(),
            bill_id_var,
            'lab_test',
            NULL,
            'Blood test panel',
            1,
            lab_fee,
            lab_fee
        ),
        (
            generate_bill_item_id(),
            bill_id_var,
            'medication',
            NULL,
            'Prescribed medications',
            1,
            medication_fee,
            medication_fee
        );
        
    END LOOP;
    
    RAISE NOTICE '✅ Additional sample bills inserted';
END $$;

-- =====================================================
-- 5. CREATE VIEWS FOR EASY DATA ACCESS
-- =====================================================

-- View for doctor schedule with details
CREATE OR REPLACE VIEW doctor_schedule_view AS
SELECT 
    s.schedule_id,
    s.doctor_id,
    p.full_name as doctor_name,
    d.specialization,
    dept.name as department_name,
    s.day_of_week,
    CASE s.day_of_week
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday'
        WHEN 2 THEN 'Tuesday'
        WHEN 3 THEN 'Wednesday'
        WHEN 4 THEN 'Thursday'
        WHEN 5 THEN 'Friday'
        WHEN 6 THEN 'Saturday'
    END as day_name,
    s.start_time,
    s.end_time,
    s.break_start_time,
    s.break_end_time,
    s.max_appointments,
    s.appointment_duration,
    s.is_active,
    s.notes
FROM schedules s
JOIN doctors d ON s.doctor_id = d.doctor_id
JOIN profiles p ON d.profile_id = p.id
LEFT JOIN departments dept ON d.department_id = dept.department_id
WHERE s.is_active = true
ORDER BY s.doctor_id, s.day_of_week;

-- View for billing with patient details
CREATE OR REPLACE VIEW billing_details_view AS
SELECT 
    b.bill_id,
    b.invoice_number,
    b.patient_id,
    p_profile.full_name as patient_name,
    p_profile.email as patient_email,
    b.appointment_id,
    b.bill_date,
    b.due_date,
    b.status,
    b.subtotal,
    b.tax_amount,
    b.discount_amount,
    b.insurance_coverage,
    b.total_amount,
    COALESCE(SUM(pay.amount), 0) as paid_amount,
    (b.total_amount - COALESCE(SUM(pay.amount), 0)) as remaining_amount,
    b.notes,
    b.created_at
FROM billing b
JOIN patients pat ON b.patient_id = pat.patient_id
JOIN profiles p_profile ON pat.profile_id = p_profile.id
LEFT JOIN payments pay ON b.bill_id = pay.bill_id AND pay.status = 'completed'
GROUP BY b.bill_id, b.invoice_number, b.patient_id, p_profile.full_name, 
         p_profile.email, b.appointment_id, b.bill_date, b.due_date, 
         b.status, b.subtotal, b.tax_amount, b.discount_amount, 
         b.insurance_coverage, b.total_amount, b.notes, b.created_at
ORDER BY b.bill_date DESC;

-- View for doctor availability
CREATE OR REPLACE VIEW doctor_availability_view AS
SELECT 
    a.availability_id,
    a.doctor_id,
    p.full_name as doctor_name,
    d.specialization,
    dept.name as department_name,
    a.date,
    TO_CHAR(a.date, 'Day') as day_name,
    a.start_time,
    a.end_time,
    a.is_available,
    a.reason,
    a.max_appointments,
    a.appointment_duration,
    -- Count existing appointments for this date
    COALESCE(apt_count.appointment_count, 0) as booked_appointments,
    (a.max_appointments - COALESCE(apt_count.appointment_count, 0)) as available_slots
FROM availability a
JOIN doctors d ON a.doctor_id = d.doctor_id
JOIN profiles p ON d.profile_id = p.id
LEFT JOIN departments dept ON d.department_id = dept.department_id
LEFT JOIN (
    SELECT 
        doctor_id,
        DATE(appointment_datetime) as appointment_date,
        COUNT(*) as appointment_count
    FROM appointments 
    WHERE status IN ('scheduled', 'confirmed')
    GROUP BY doctor_id, DATE(appointment_datetime)
) apt_count ON a.doctor_id = apt_count.doctor_id AND a.date = apt_count.appointment_date
WHERE a.date >= CURRENT_DATE
ORDER BY a.doctor_id, a.date;

-- =====================================================
-- 6. CREATE SUMMARY FUNCTIONS
-- =====================================================

-- Function to get billing summary
CREATE OR REPLACE FUNCTION get_billing_summary()
RETURNS TABLE(
    total_bills bigint,
    total_amount numeric,
    paid_amount numeric,
    pending_amount numeric,
    overdue_amount numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_bills,
        SUM(b.total_amount) as total_amount,
        SUM(CASE WHEN b.status = 'paid' THEN b.total_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN b.status = 'pending' THEN b.total_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN b.status = 'overdue' THEN b.total_amount ELSE 0 END) as overdue_amount
    FROM billing b;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Sample data inserted successfully!';
    RAISE NOTICE '📅 Doctor schedules created for all existing doctors';
    RAISE NOTICE '🗓️ Availability generated for next 30 days';
    RAISE NOTICE '💰 Sample billing and payment data created';
    RAISE NOTICE '👁️ Views created for easy data access';
    RAISE NOTICE '📊 Summary functions available';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 You can now query:';
    RAISE NOTICE '   - doctor_schedule_view';
    RAISE NOTICE '   - billing_details_view'; 
    RAISE NOTICE '   - doctor_availability_view';
    RAISE NOTICE '   - get_billing_summary()';
END $$;
