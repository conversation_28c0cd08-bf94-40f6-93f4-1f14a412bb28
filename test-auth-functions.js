// =====================================================
// TEST AUTHENTICATION FUNCTIONS
// =====================================================
// This script tests the authentication functions directly

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ciasxktujslgsdgylimv.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.R2IieQgCFPQ5sgEqPSJmF9uB1hZasJHg5enl2alJpn4';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  full_name: 'Nguyễn Văn Test',
  phone_number: '**********',
  role: 'patient',
  date_of_birth: '1990-01-01',
  gender: 'male',
  address: '123 Test Street, Ho Chi Minh City'
};

const testDoctor = {
  email: '<EMAIL>',
  password: 'DoctorPassword123!',
  full_name: 'Bác sĩ Test',
  phone_number: '**********',
  role: 'doctor',
  specialty: 'Nội tổng hợp',
  license_number: 'VN-HN-1234',
  qualification: 'Thạc sĩ Y khoa',
  department_id: 'DEPT001'
};

// Test functions
async function testConnection() {
  console.log('\n🔗 Testing Supabase connection...');
  try {
    const { data, error } = await supabase.from('profiles').select('id').limit(1);
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }
    console.log('✅ Connection successful');
    return true;
  } catch (err) {
    console.error('❌ Connection error:', err.message);
    return false;
  }
}

async function testSignUp(userData) {
  console.log(`\n📝 Testing sign up for: ${userData.email}`);
  try {
    // 1. Create auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password,
      options: {
        data: {
          full_name: userData.full_name,
          phone_number: userData.phone_number,
          role: userData.role,
          specialty: userData.specialty,
          license_number: userData.license_number,
          qualification: userData.qualification,
          department_id: userData.department_id,
          date_of_birth: userData.date_of_birth,
          gender: userData.gender,
          address: userData.address,
        }
      }
    });

    if (authError) {
      console.error('❌ Auth signup error:', authError.message);
      return { success: false, error: authError.message };
    }

    if (!authData.user) {
      console.error('❌ No user returned from auth signup');
      return { success: false, error: 'No user returned' };
    }

    console.log('✅ Auth user created:', authData.user.id);

    // 2. Check if profile was created by trigger
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds

    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError || !profileData) {
      console.log('⚠️ Profile not created by trigger, creating manually...');

      // Create profile manually
      const { data: manualProfile, error: manualError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          phone_number: userData.phone_number,
          role: userData.role,
          email_verified: false,
          is_active: true
        })
        .select()
        .single();

      if (manualError) {
        console.error('❌ Manual profile creation failed:', manualError.message);
        return { success: false, error: manualError.message };
      }

      console.log('✅ Profile created manually');
    } else {
      console.log('✅ Profile created by trigger');
    }

    // 3. Create role-specific profile
    if (userData.role === 'doctor') {
      const { error: doctorError } = await supabase
        .from('doctors')
        .insert({
          profile_id: authData.user.id,
          specialization: userData.specialty,
          license_number: userData.license_number,
          qualification: userData.qualification,
          department_id: userData.department_id,
        });

      if (doctorError) {
        console.error('❌ Doctor profile creation failed:', doctorError.message);
      } else {
        console.log('✅ Doctor profile created');
      }
    } else if (userData.role === 'patient') {
      const { error: patientError } = await supabase
        .from('patients')
        .insert({
          profile_id: authData.user.id,
          date_of_birth: userData.date_of_birth,
          gender: userData.gender,
          address: userData.address ? { street: userData.address } : {},
        });

      if (patientError) {
        console.error('❌ Patient profile creation failed:', patientError.message);
      } else {
        console.log('✅ Patient profile created');
      }
    }

    return {
      success: true,
      user: authData.user,
      profile: profileData
    };

  } catch (error) {
    console.error('❌ Signup error:', error.message);
    return { success: false, error: error.message };
  }
}

async function testSignIn(email, password) {
  console.log(`\n🔐 Testing sign in for: ${email}`);
  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError) {
      console.error('❌ Auth signin error:', authError.message);
      return { success: false, error: authError.message };
    }

    if (!authData.user || !authData.session) {
      console.error('❌ No user or session returned');
      return { success: false, error: 'No user or session returned' };
    }

    console.log('✅ Auth signin successful');

    // Get user profile
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError || !profileData) {
      console.error('❌ Profile not found:', profileError?.message);
      return { success: false, error: 'Profile not found' };
    }

    console.log('✅ Profile retrieved:', {
      id: profileData.id,
      email: profileData.email,
      full_name: profileData.full_name,
      role: profileData.role
    });

    return {
      success: true,
      user: authData.user,
      session: authData.session,
      profile: profileData
    };

  } catch (error) {
    console.error('❌ Signin error:', error.message);
    return { success: false, error: error.message };
  }
}

async function testProfileRetrieval(userId) {
  console.log(`\n👤 Testing profile retrieval for: ${userId}`);
  try {
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('❌ Profile retrieval failed:', profileError.message);
      return { success: false, error: profileError.message };
    }

    console.log('✅ Profile retrieved successfully:', {
      id: profileData.id,
      email: profileData.email,
      full_name: profileData.full_name,
      role: profileData.role,
      is_active: profileData.is_active
    });

    return { success: true, profile: profileData };

  } catch (error) {
    console.error('❌ Profile retrieval error:', error.message);
    return { success: false, error: error.message };
  }
}

async function cleanupTestUsers() {
  console.log('\n🧹 Cleaning up test users...');
  try {
    // Delete test users from auth.users (this will cascade to profiles)
    const { error: deleteError } = await supabase.auth.admin.deleteUser(testUser.email);
    if (deleteError && !deleteError.message.includes('User not found')) {
      console.log('⚠️ Error deleting test patient:', deleteError.message);
    }

    const { error: deleteError2 } = await supabase.auth.admin.deleteUser(testDoctor.email);
    if (deleteError2 && !deleteError2.message.includes('User not found')) {
      console.log('⚠️ Error deleting test doctor:', deleteError2.message);
    }

    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log('⚠️ Cleanup error:', error.message);
  }
}

async function runTests() {
  console.log('🏥 HOSPITAL MANAGEMENT - AUTHENTICATION TESTS');
  console.log('=' .repeat(60));
  console.log(`📡 Supabase URL: ${supabaseUrl}`);
  console.log(`🔑 Service Key: ${supabaseServiceKey ? '***' + supabaseServiceKey.slice(-4) : 'Not set'}`);

  // Test connection
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.log('❌ Cannot proceed without connection');
    return;
  }

  // Cleanup first
  await cleanupTestUsers();

  let testResults = {
    patientSignup: false,
    doctorSignup: false,
    patientSignin: false,
    doctorSignin: false,
    profileRetrieval: false
  };

  // Test patient signup
  console.log('\n' + '='.repeat(40));
  console.log('TESTING PATIENT REGISTRATION');
  console.log('='.repeat(40));

  const patientSignupResult = await testSignUp(testUser);
  testResults.patientSignup = patientSignupResult.success;

  if (patientSignupResult.success) {
    // Test patient signin
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    const patientSigninResult = await testSignIn(testUser.email, testUser.password);
    testResults.patientSignin = patientSigninResult.success;

    if (patientSigninResult.success) {
      // Test profile retrieval
      const profileResult = await testProfileRetrieval(patientSigninResult.user.id);
      testResults.profileRetrieval = profileResult.success;
    }
  }

  // Test doctor signup
  console.log('\n' + '='.repeat(40));
  console.log('TESTING DOCTOR REGISTRATION');
  console.log('='.repeat(40));

  const doctorSignupResult = await testSignUp(testDoctor);
  testResults.doctorSignup = doctorSignupResult.success;

  if (doctorSignupResult.success) {
    // Test doctor signin
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    const doctorSigninResult = await testSignIn(testDoctor.email, testDoctor.password);
    testResults.doctorSignin = doctorSigninResult.success;
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));

  console.log(`👥 Patient Registration: ${testResults.patientSignup ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`👨‍⚕️ Doctor Registration: ${testResults.doctorSignup ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🔐 Patient Login: ${testResults.patientSignin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🔐 Doctor Login: ${testResults.doctorSignin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`👤 Profile Retrieval: ${testResults.profileRetrieval ? '✅ PASS' : '❌ FAIL'}`);

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;

  console.log(`\n📈 Overall Score: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All authentication functions are working correctly!');
  } else {
    console.log('⚠️ Some authentication functions need attention.');
  }

  // Cleanup
  await cleanupTestUsers();
}

// Run the tests
runTests().catch(console.error);
