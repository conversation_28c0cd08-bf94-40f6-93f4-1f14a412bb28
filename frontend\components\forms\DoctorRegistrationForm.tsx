'use client';

// =====================================================
// DOCTOR REGISTRATION FORM - SỬ DỤNG ENUM ĐỘNG
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  useGenderEnums,
  useDoctorStatusEnums,
  useEnumDisplayName,
  useDefaultEnum,
} from '@/lib/contexts/EnumContext';
import { ENUM_CATEGORIES } from '@/lib/types/enum.types';
import { enumService } from '@/lib/services/enum.service';

interface DoctorFormData {
  fullName: string;
  email: string;
  phoneNumber: string;
  licenseNumber: string;
  specialization: string;
  qualification: string;
  experienceYears: number;
  consultationFee: number;
  gender: string;
  status: string;
  bio: string;
  languagesSpoken: string[];
}

export function DoctorRegistrationForm() {
  // Sử dụng enum hooks
  const genderOptions = useGenderEnums();
  const statusOptions = useDoctorStatusEnums();
  const defaultStatus = useDefaultEnum(ENUM_CATEGORIES.DOCTOR_STATUS);

  // Form state
  const [formData, setFormData] = useState<DoctorFormData>({
    fullName: '',
    email: '',
    phoneNumber: '',
    licenseNumber: '',
    specialization: '',
    qualification: '',
    experienceYears: 0,
    consultationFee: 0,
    gender: '',
    status: defaultStatus?.enum_key || 'active',
    bio: '',
    languagesSpoken: [],
  });

  const [specializations, setSpecializations] = useState<Array<{value: string, label: string}>>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load specializations (có thể từ enum hoặc database)
  useEffect(() => {
    // Giả lập load specializations
    setSpecializations([
      { value: 'cardiology', label: 'Tim mạch' },
      { value: 'neurology', label: 'Thần kinh' },
      { value: 'pediatrics', label: 'Nhi khoa' },
      { value: 'orthopedics', label: 'Chỉnh hình' },
      { value: 'dermatology', label: 'Da liễu' },
      { value: 'general', label: 'Đa khoa' },
    ]);
  }, []);

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Họ tên là bắt buộc';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email không hợp lệ';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Số điện thoại là bắt buộc';
    } else if (!/^0\d{9}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Số điện thoại phải có 10 số và bắt đầu bằng 0';
    }

    if (!formData.licenseNumber.trim()) {
      newErrors.licenseNumber = 'Số giấy phép hành nghề là bắt buộc';
    } else if (!/^VN-[A-Z]{2}-\d{4}$/.test(formData.licenseNumber)) {
      newErrors.licenseNumber = 'Số giấy phép phải có định dạng VN-XX-0000';
    }

    if (!formData.specialization) {
      newErrors.specialization = 'Chuyên khoa là bắt buộc';
    }

    if (!formData.qualification.trim()) {
      newErrors.qualification = 'Bằng cấp là bắt buộc';
    }

    if (!formData.gender) {
      newErrors.gender = 'Giới tính là bắt buộc';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Validate enum values
      const isGenderValid = await enumService.validateEnum(ENUM_CATEGORIES.GENDER, formData.gender);
      const isStatusValid = await enumService.validateEnum(ENUM_CATEGORIES.DOCTOR_STATUS, formData.status);

      if (!isGenderValid) {
        setErrors({ gender: 'Giới tính không hợp lệ' });
        return;
      }

      if (!isStatusValid) {
        setErrors({ status: 'Trạng thái không hợp lệ' });
        return;
      }

      // Simulate API call
      console.log('Submitting doctor registration:', formData);
      
      // Get display names for confirmation
      const genderDisplay = await enumService.getEnumDisplayName(ENUM_CATEGORIES.GENDER, formData.gender, 'vi');
      const statusDisplay = await enumService.getEnumDisplayName(ENUM_CATEGORIES.DOCTOR_STATUS, formData.status, 'vi');
      
      alert(`Đăng ký thành công!\nGiới tính: ${genderDisplay}\nTrạng thái: ${statusDisplay}`);
      
      // Reset form
      setFormData({
        fullName: '',
        email: '',
        phoneNumber: '',
        licenseNumber: '',
        specialization: '',
        qualification: '',
        experienceYears: 0,
        consultationFee: 0,
        gender: '',
        status: defaultStatus?.enum_key || 'active',
        bio: '',
        languagesSpoken: [],
      });
      setErrors({});
      
    } catch (error) {
      console.error('Registration error:', error);
      alert('Có lỗi xảy ra khi đăng ký. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  // Handle input change
  const handleInputChange = (field: keyof DoctorFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-center mb-6">Đăng ký Bác sĩ</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Họ và tên <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.fullName ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="Nhập họ và tên"
            />
            {errors.fullName && <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Email <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Số điện thoại <span className="text-red-500">*</span>
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.phoneNumber ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="**********"
            />
            {errors.phoneNumber && <p className="text-red-500 text-sm mt-1">{errors.phoneNumber}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Số giấy phép hành nghề <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.licenseNumber}
              onChange={(e) => handleInputChange('licenseNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.licenseNumber ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="VN-TM-1234"
            />
            {errors.licenseNumber && <p className="text-red-500 text-sm mt-1">{errors.licenseNumber}</p>}
          </div>
        </div>

        {/* Professional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Chuyên khoa <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.specialization}
              onChange={(e) => handleInputChange('specialization', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.specialization ? 'border-red-500' : 'border-gray-300'}`}
            >
              <option value="">Chọn chuyên khoa</option>
              {specializations.map((spec) => (
                <option key={spec.value} value={spec.value}>
                  {spec.label}
                </option>
              ))}
            </select>
            {errors.specialization && <p className="text-red-500 text-sm mt-1">{errors.specialization}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Bằng cấp <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.qualification}
              onChange={(e) => handleInputChange('qualification', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.qualification ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="Bác sĩ Y khoa, Thạc sĩ Y học..."
            />
            {errors.qualification && <p className="text-red-500 text-sm mt-1">{errors.qualification}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Số năm kinh nghiệm</label>
            <input
              type="number"
              min="0"
              max="50"
              value={formData.experienceYears}
              onChange={(e) => handleInputChange('experienceYears', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Phí khám (VNĐ)</label>
            <input
              type="number"
              min="0"
              value={formData.consultationFee}
              onChange={(e) => handleInputChange('consultationFee', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="500000"
            />
          </div>
        </div>

        {/* Enum Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Giới tính <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.gender}
              onChange={(e) => handleInputChange('gender', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${errors.gender ? 'border-red-500' : 'border-gray-300'}`}
            >
              <option value="">Chọn giới tính</option>
              {genderOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Trạng thái</label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                  {option.isDefault && ' (Mặc định)'}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Bio */}
        <div>
          <label className="block text-sm font-medium mb-2">Tiểu sử</label>
          <textarea
            value={formData.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="Mô tả ngắn về kinh nghiệm và chuyên môn..."
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={loading}
            className="px-8 py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Đang đăng ký...' : 'Đăng ký Bác sĩ'}
          </button>
        </div>
      </form>
    </div>
  );
}
