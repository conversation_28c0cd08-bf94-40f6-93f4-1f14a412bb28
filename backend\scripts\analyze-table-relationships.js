const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase credentials');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Define expected table relationships
const TABLE_RELATIONSHIPS = {
    // Core entity tables
    profiles: {
        type: 'core',
        description: 'User profiles (auth users)',
        relationships: {
            outgoing: ['doctors.profile_id', 'patients.profile_id', 'admins.profile_id'],
            incoming: []
        }
    },
    
    doctors: {
        type: 'core',
        description: 'Doctor information',
        relationships: {
            outgoing: ['appointments.doctor_id', 'medical_records.doctor_id', 'schedules.doctor_id', 'availability.doctor_id'],
            incoming: ['profiles.id -> profile_id', 'departments.department_id -> department_id', 'specialties.id -> specialization']
        }
    },
    
    patients: {
        type: 'core', 
        description: 'Patient information',
        relationships: {
            outgoing: ['appointments.patient_id', 'medical_records.patient_id', 'billing.patient_id'],
            incoming: ['profiles.id -> profile_id']
        }
    },
    
    departments: {
        type: 'core',
        description: 'Hospital departments',
        relationships: {
            outgoing: ['doctors.department_id', 'rooms.department_id'],
            incoming: []
        }
    },
    
    appointments: {
        type: 'core',
        description: 'Patient appointments',
        relationships: {
            outgoing: ['medical_records.appointment_id', 'billing.appointment_id'],
            incoming: ['patients.patient_id', 'doctors.doctor_id', 'rooms.room_id']
        }
    },
    
    rooms: {
        type: 'core',
        description: 'Hospital rooms',
        relationships: {
            outgoing: ['appointments.room_id'],
            incoming: ['departments.department_id', 'room_types.id -> room_type']
        }
    },
    
    medical_records: {
        type: 'core',
        description: 'Patient medical records',
        relationships: {
            outgoing: ['prescriptions.record_id'],
            incoming: ['patients.patient_id', 'doctors.doctor_id', 'appointments.appointment_id']
        }
    },
    
    prescriptions: {
        type: 'core',
        description: 'Medical prescriptions',
        relationships: {
            outgoing: [],
            incoming: ['medical_records.record_id', 'medications.id -> medication_id']
        }
    },
    
    // Missing tables that need to be created
    billing: {
        type: 'missing',
        description: 'Patient billing information',
        relationships: {
            outgoing: ['payments.bill_id', 'bill_items.bill_id'],
            incoming: ['patients.patient_id', 'appointments.appointment_id']
        }
    },
    
    payments: {
        type: 'missing',
        description: 'Payment transactions',
        relationships: {
            outgoing: [],
            incoming: ['billing.bill_id', 'payment_methods.id -> payment_method_id']
        }
    },
    
    schedules: {
        type: 'missing',
        description: 'Doctor weekly schedules',
        relationships: {
            outgoing: [],
            incoming: ['doctors.doctor_id']
        }
    },
    
    availability: {
        type: 'missing',
        description: 'Doctor daily availability',
        relationships: {
            outgoing: [],
            incoming: ['doctors.doctor_id']
        }
    },
    
    // Dynamic tables
    specialties: {
        type: 'dynamic',
        description: 'Medical specialties',
        relationships: {
            outgoing: ['doctors.specialization'],
            incoming: []
        }
    },
    
    departments_enum: {
        type: 'dynamic',
        description: 'Department types',
        relationships: {
            outgoing: [],
            incoming: []
        }
    },
    
    room_types: {
        type: 'dynamic',
        description: 'Room type definitions',
        relationships: {
            outgoing: ['rooms.room_type'],
            incoming: []
        }
    },
    
    diagnosis: {
        type: 'dynamic',
        description: 'Medical diagnoses',
        relationships: {
            outgoing: ['medical_records.diagnosis'],
            incoming: []
        }
    },
    
    medications: {
        type: 'dynamic',
        description: 'Medication catalog',
        relationships: {
            outgoing: ['prescriptions.medication_id'],
            incoming: []
        }
    },
    
    status_values: {
        type: 'dynamic',
        description: 'System status values',
        relationships: {
            outgoing: ['appointments.status', 'billing.status', 'payments.status'],
            incoming: []
        }
    },
    
    payment_methods: {
        type: 'dynamic',
        description: 'Payment method options',
        relationships: {
            outgoing: ['payments.payment_method_id'],
            incoming: []
        }
    }
};

async function checkTableExists(tableName) {
    try {
        const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
        
        return !error;
    } catch (err) {
        return false;
    }
}

async function analyzeRelationships() {
    console.log('🔍 ANALYZING TABLE RELATIONSHIPS\n');
    console.log('=' .repeat(80));
    
    const existingTables = [];
    const missingTables = [];
    const dynamicTables = [];
    
    // Check which tables exist
    for (const [tableName, tableInfo] of Object.entries(TABLE_RELATIONSHIPS)) {
        const exists = await checkTableExists(tableName);
        
        if (exists) {
            if (tableInfo.type === 'dynamic') {
                dynamicTables.push(tableName);
            } else {
                existingTables.push(tableName);
            }
        } else {
            missingTables.push(tableName);
        }
    }
    
    console.log('\n📊 TABLE STATUS SUMMARY:');
    console.log(`✅ Existing Core Tables: ${existingTables.length}`);
    console.log(`❌ Missing Core Tables: ${missingTables.length}`);
    console.log(`🔧 Dynamic Tables: ${dynamicTables.length}`);
    
    // Analyze relationships for existing tables
    console.log('\n🔗 RELATIONSHIP ANALYSIS:\n');
    
    for (const tableName of existingTables) {
        const tableInfo = TABLE_RELATIONSHIPS[tableName];
        console.log(`📋 ${tableName.toUpperCase()}`);
        console.log(`   Description: ${tableInfo.description}`);
        
        // Check outgoing relationships
        if (tableInfo.relationships.outgoing.length > 0) {
            console.log('   🔗 References (outgoing):');
            for (const ref of tableInfo.relationships.outgoing) {
                const [targetTable] = ref.split('.');
                const exists = await checkTableExists(targetTable);
                const status = exists ? '✅' : '❌';
                console.log(`      ${status} ${ref}`);
            }
        }
        
        // Check incoming relationships
        if (tableInfo.relationships.incoming.length > 0) {
            console.log('   🔙 Referenced by (incoming):');
            for (const ref of tableInfo.relationships.incoming) {
                const [sourceTable] = ref.split('.')[0].split(' ')[0];
                const exists = await checkTableExists(sourceTable);
                const status = exists ? '✅' : '❌';
                console.log(`      ${status} ${ref}`);
            }
        }
        
        console.log('');
    }
    
    // Show missing tables and their impact
    if (missingTables.length > 0) {
        console.log('\n❌ MISSING TABLES AND THEIR IMPACT:\n');
        
        for (const tableName of missingTables) {
            const tableInfo = TABLE_RELATIONSHIPS[tableName];
            console.log(`📋 ${tableName.toUpperCase()} (MISSING)`);
            console.log(`   Description: ${tableInfo.description}`);
            console.log(`   Impact: Breaks ${tableInfo.relationships.incoming.length} incoming relationships`);
            
            if (tableInfo.relationships.incoming.length > 0) {
                console.log('   🔗 Affected relationships:');
                for (const ref of tableInfo.relationships.incoming) {
                    console.log(`      ⚠️  ${ref}`);
                }
            }
            console.log('');
        }
    }
    
    // Show dynamic tables status
    console.log('\n🔧 DYNAMIC TABLES STATUS:\n');
    
    for (const tableName of dynamicTables) {
        const tableInfo = TABLE_RELATIONSHIPS[tableName];
        console.log(`📋 ${tableName.toUpperCase()}`);
        console.log(`   Description: ${tableInfo.description}`);
        
        if (tableInfo.relationships.outgoing.length > 0) {
            console.log('   🔗 Used by:');
            for (const ref of tableInfo.relationships.outgoing) {
                const [targetTable] = ref.split('.');
                const exists = await checkTableExists(targetTable);
                const status = exists ? '✅' : '❌';
                console.log(`      ${status} ${ref}`);
            }
        }
        console.log('');
    }
    
    // Generate recommendations
    console.log('\n💡 RECOMMENDATIONS:\n');
    
    if (missingTables.length > 0) {
        console.log('🔧 MISSING TABLES TO CREATE:');
        for (const tableName of missingTables) {
            console.log(`   • ${tableName} - ${TABLE_RELATIONSHIPS[tableName].description}`);
        }
        console.log('\n📝 Action: Run the SQL scripts to create missing tables');
        console.log('   1. scripts/create-missing-tables.sql');
        console.log('   2. scripts/insert-sample-data-missing-tables.sql');
    }
    
    if (existingTables.length > 0 && missingTables.length === 0) {
        console.log('✅ All core tables exist - relationships are complete!');
    }
    
    console.log('\n🔧 DYNAMIC TABLE SYSTEM:');
    console.log(`   ✅ ${dynamicTables.length}/7 dynamic tables implemented`);
    console.log('   ✅ Successfully replaced enum system with dynamic tables');
    
    // Calculate relationship completeness
    let totalRelationships = 0;
    let completeRelationships = 0;
    
    for (const [tableName, tableInfo] of Object.entries(TABLE_RELATIONSHIPS)) {
        const tableExists = await checkTableExists(tableName);
        if (!tableExists) continue;
        
        for (const ref of tableInfo.relationships.outgoing) {
            totalRelationships++;
            const [targetTable] = ref.split('.');
            const targetExists = await checkTableExists(targetTable);
            if (targetExists) completeRelationships++;
        }
        
        for (const ref of tableInfo.relationships.incoming) {
            totalRelationships++;
            const [sourceTable] = ref.split('.')[0].split(' ')[0];
            const sourceExists = await checkTableExists(sourceTable);
            if (sourceExists) completeRelationships++;
        }
    }
    
    const completenessPercentage = totalRelationships > 0 ? 
        Math.round((completeRelationships / totalRelationships) * 100) : 100;
    
    console.log('\n📊 RELATIONSHIP COMPLETENESS:');
    console.log(`   ${completeRelationships}/${totalRelationships} relationships complete (${completenessPercentage}%)`);
    
    if (completenessPercentage < 100) {
        console.log('   ⚠️  Some relationships are broken due to missing tables');
    } else {
        console.log('   ✅ All relationships are complete!');
    }
}

async function main() {
    console.log('🏥 HOSPITAL MANAGEMENT - TABLE RELATIONSHIP ANALYZER');
    console.log('=' .repeat(80));
    console.log(`📡 Supabase URL: ${supabaseUrl}`);
    console.log(`🔑 Service Key: ${supabaseServiceKey ? '***' + supabaseServiceKey.slice(-4) : 'Not set'}`);
    
    try {
        // Test connection first
        console.log('\n🔗 Testing Supabase connection...');
        const { data, error } = await supabase.from('profiles').select('id').limit(1);
        
        if (error) {
            console.log('❌ Connection failed:', error.message);
            process.exit(1);
        }
        
        console.log('✅ Connection successful');
        
        // Analyze relationships
        await analyzeRelationships();
        
        console.log('\n✅ Relationship analysis completed!');
        
    } catch (error) {
        console.error('\n❌ Analysis failed:', error.message);
        process.exit(1);
    }
}

// Run the analysis
main();
