const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase credentials');
    console.log('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSqlScript(scriptPath) {
    try {
        console.log(`\n🔄 Running SQL script: ${scriptPath}`);
        
        // Read the SQL file
        const sqlContent = fs.readFileSync(scriptPath, 'utf8');
        
        // Split into individual statements (basic splitting by semicolon)
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📝 Found ${statements.length} SQL statements to execute`);
        
        let successCount = 0;
        let errorCount = 0;
        
        // Execute each statement
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            // Skip empty statements and comments
            if (!statement || statement.startsWith('--') || statement.trim() === '') {
                continue;
            }
            
            try {
                console.log(`\n⚡ Executing statement ${i + 1}/${statements.length}...`);
                
                // Use rpc to execute raw SQL
                const { data, error } = await supabase.rpc('exec_sql', { 
                    sql_query: statement 
                });
                
                if (error) {
                    // If rpc doesn't work, try direct query for simple statements
                    if (error.message.includes('function exec_sql') || error.message.includes('does not exist')) {
                        console.log('⚠️  RPC not available, trying direct execution...');
                        
                        // For CREATE TABLE statements, we need to use a different approach
                        if (statement.toUpperCase().includes('CREATE TABLE') || 
                            statement.toUpperCase().includes('CREATE INDEX') ||
                            statement.toUpperCase().includes('CREATE FUNCTION') ||
                            statement.toUpperCase().includes('CREATE TRIGGER')) {
                            
                            console.log('❌ Cannot execute DDL statements without RPC function');
                            console.log('📋 Statement:', statement.substring(0, 100) + '...');
                            console.log('💡 Please run this SQL manually in Supabase SQL Editor');
                            errorCount++;
                            continue;
                        }
                        
                        // Try as a simple query for SELECT/INSERT statements
                        const { data: directData, error: directError } = await supabase
                            .from('dummy') // This will fail but might give us better error info
                            .select('*')
                            .limit(1);
                        
                        if (directError) {
                            console.log('❌ Error:', directError.message);
                            errorCount++;
                            continue;
                        }
                    } else {
                        console.log('❌ Error:', error.message);
                        errorCount++;
                        continue;
                    }
                }
                
                console.log('✅ Success');
                successCount++;
                
            } catch (err) {
                console.log('❌ Exception:', err.message);
                errorCount++;
            }
        }
        
        console.log('\n📊 EXECUTION SUMMARY:');
        console.log(`✅ Successful: ${successCount}`);
        console.log(`❌ Failed: ${errorCount}`);
        console.log(`📝 Total: ${statements.length}`);
        
        if (errorCount > 0) {
            console.log('\n⚠️  Some statements failed. You may need to run them manually in Supabase SQL Editor.');
            console.log('📁 Script location:', scriptPath);
        }
        
        return { successCount, errorCount, total: statements.length };
        
    } catch (error) {
        console.error('\n❌ Error reading or executing SQL script:', error.message);
        throw error;
    }
}

async function main() {
    const scriptArg = process.argv[2];
    
    if (!scriptArg) {
        console.log('Usage: node run-sql-script.js <path-to-sql-file>');
        console.log('Example: node run-sql-script.js ../../scripts/create-missing-tables.sql');
        process.exit(1);
    }
    
    // Resolve the script path
    const scriptPath = path.resolve(scriptArg);
    
    if (!fs.existsSync(scriptPath)) {
        console.error(`❌ SQL script not found: ${scriptPath}`);
        process.exit(1);
    }
    
    console.log('🏥 HOSPITAL MANAGEMENT - SQL SCRIPT RUNNER');
    console.log('=' .repeat(60));
    console.log(`📡 Supabase URL: ${supabaseUrl}`);
    console.log(`🔑 Service Key: ${supabaseServiceKey ? '***' + supabaseServiceKey.slice(-4) : 'Not set'}`);
    console.log(`📄 Script: ${scriptPath}`);
    
    try {
        // Test connection first
        console.log('\n🔗 Testing Supabase connection...');
        const { data, error } = await supabase.from('profiles').select('id').limit(1);
        
        if (error) {
            console.log('❌ Connection failed:', error.message);
            process.exit(1);
        }
        
        console.log('✅ Connection successful');
        
        // Run the SQL script
        const result = await runSqlScript(scriptPath);
        
        if (result.errorCount === 0) {
            console.log('\n🎉 All SQL statements executed successfully!');
        } else {
            console.log('\n⚠️  Some statements failed. Check the output above for details.');
            console.log('\n💡 MANUAL EXECUTION REQUIRED:');
            console.log('1. Open Supabase Dashboard → SQL Editor');
            console.log('2. Copy and paste the failed statements');
            console.log('3. Execute them manually');
        }
        
    } catch (error) {
        console.error('\n❌ Script execution failed:', error.message);
        process.exit(1);
    }
}

// Run the script
main();
