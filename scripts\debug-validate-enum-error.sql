-- =====================================================
-- DEBUG VALIDATE_ENUM ERROR
-- =====================================================
-- This script helps find where validate_enum is being called from

-- =====================================================
-- 1. CHECK ALL TRIGGERS
-- =====================================================

-- Check all triggers and their definitions
SELECT 
    schemaname,
    tablename,
    triggername,
    pg_get_triggerdef(oid) as trigger_definition
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE n.nspname = 'public'
ORDER BY schemaname, tablename, triggername;

-- =====================================================
-- 2. CHECK ALL CONSTRAINTS
-- =====================================================

-- Check all check constraints that might reference validate_enum
SELECT 
    tc.table_schema,
    tc.table_name,
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_schema = 'public'
    AND cc.check_clause LIKE '%validate_enum%'
ORDER BY tc.table_name;

-- =====================================================
-- 3. CHECK ALL POLICIES
-- =====================================================

-- Check all RLS policies that might reference validate_enum
SELECT 
    schemaname,
    tablename,
    policyname,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
    AND (qual LIKE '%validate_enum%' OR with_check LIKE '%validate_enum%')
ORDER BY tablename, policyname;

-- =====================================================
-- 4. CHECK COLUMN DEFAULTS
-- =====================================================

-- Check if any column defaults reference validate_enum
SELECT 
    table_schema,
    table_name,
    column_name,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public'
    AND column_default LIKE '%validate_enum%'
ORDER BY table_name, column_name;

-- =====================================================
-- 5. CHECK ALL FUNCTIONS FOR REFERENCES
-- =====================================================

-- Check if any existing functions call validate_enum
SELECT 
    n.nspname AS schema_name,
    p.proname AS function_name,
    pg_get_functiondef(p.oid) AS definition
FROM pg_proc p
LEFT JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
    AND pg_get_functiondef(p.oid) LIKE '%validate_enum%'
ORDER BY schema_name, function_name;

-- =====================================================
-- 6. CHECK VIEWS
-- =====================================================

-- Check if any views reference validate_enum
SELECT 
    schemaname,
    viewname,
    definition
FROM pg_views
WHERE schemaname = 'public'
    AND definition LIKE '%validate_enum%'
ORDER BY viewname;

-- =====================================================
-- 7. SEARCH IN ALL TEXT FIELDS
-- =====================================================

-- Search for validate_enum in all possible places
DO $$
DECLARE
    rec RECORD;
    search_text TEXT := 'validate_enum';
    found_count INTEGER := 0;
BEGIN
    RAISE NOTICE 'Searching for references to validate_enum...';
    RAISE NOTICE '';
    
    -- Check triggers
    FOR rec IN 
        SELECT 'TRIGGER' as type, triggername as name, pg_get_triggerdef(oid) as definition
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE n.nspname = 'public'
    LOOP
        IF rec.definition LIKE '%' || search_text || '%' THEN
            RAISE NOTICE 'FOUND in %: %', rec.type, rec.name;
            RAISE NOTICE 'Definition: %', rec.definition;
            RAISE NOTICE '';
            found_count := found_count + 1;
        END IF;
    END LOOP;
    
    -- Check functions
    FOR rec IN 
        SELECT 'FUNCTION' as type, proname as name, pg_get_functiondef(oid) as definition
        FROM pg_proc p
        LEFT JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
    LOOP
        IF rec.definition LIKE '%' || search_text || '%' THEN
            RAISE NOTICE 'FOUND in %: %', rec.type, rec.name;
            RAISE NOTICE 'Definition: %', rec.definition;
            RAISE NOTICE '';
            found_count := found_count + 1;
        END IF;
    END LOOP;
    
    -- Check policies
    FOR rec IN 
        SELECT 'POLICY' as type, policyname as name, 
               COALESCE(qual, '') || ' ' || COALESCE(with_check, '') as definition
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        IF rec.definition LIKE '%' || search_text || '%' THEN
            RAISE NOTICE 'FOUND in %: %', rec.type, rec.name;
            RAISE NOTICE 'Definition: %', rec.definition;
            RAISE NOTICE '';
            found_count := found_count + 1;
        END IF;
    END LOOP;
    
    IF found_count = 0 THEN
        RAISE NOTICE '✅ No references to validate_enum found in database objects';
    ELSE
        RAISE NOTICE '⚠️ Found % references to validate_enum', found_count;
    END IF;
END $$;

-- =====================================================
-- 8. CHECK SPECIFIC TABLES FOR CONSTRAINTS
-- =====================================================

-- Check profiles table constraints
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'public.profiles'::regclass
ORDER BY conname;

-- Check doctors table constraints  
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'public.doctors'::regclass
ORDER BY conname;

-- Check patients table constraints
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'public.patients'::regclass
ORDER BY conname;

-- =====================================================
-- 9. FINAL SUMMARY
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔍 DEBUG ANALYSIS COMPLETED';
    RAISE NOTICE '';
    RAISE NOTICE 'If validate_enum references are found above,';
    RAISE NOTICE 'they need to be removed to fix the authentication error.';
    RAISE NOTICE '';
    RAISE NOTICE 'If no references are found, the error might be coming from:';
    RAISE NOTICE '1. Application code calling validate_enum RPC';
    RAISE NOTICE '2. Cached query plans in Supabase';
    RAISE NOTICE '3. Edge functions or webhooks';
    RAISE NOTICE '';
END $$;
