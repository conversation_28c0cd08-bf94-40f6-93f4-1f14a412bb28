-- =====================================================
-- FIX AUTHENTICATION ISSUES
-- =====================================================
-- This script fixes RLS policies and triggers for authentication

-- =====================================================
-- 1. DROP EXISTING PROBLEMATIC POLICIES
-- =====================================================

-- Drop existing policies on profiles table
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON profiles;

-- =====================================================
-- 2. CREATE PROPER RLS POLICIES FOR PROFILES
-- =====================================================

-- Allow users to read their own profile
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Allow authenticated users to insert their own profile (for manual creation)
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow service role to manage all profiles (for admin operations)
CREATE POLICY "Service role can manage all profiles" ON profiles
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =====================================================
-- 3. CREATE/FIX PROFILE CREATION TRIGGER
-- =====================================================

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create the trigger function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_role TEXT;
    user_name TEXT;
    user_phone TEXT;
BEGIN
    -- Extract metadata from raw_user_meta_data
    user_role := COALESCE(NEW.raw_user_meta_data->>'role', 'patient');
    user_name := COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1));
    user_phone := NEW.raw_user_meta_data->>'phone_number';

    -- Insert into profiles table
    INSERT INTO public.profiles (
        id,
        email,
        full_name,
        phone_number,
        role,
        email_verified,
        phone_verified,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        user_name,
        user_phone,
        user_role,
        NEW.email_confirmed_at IS NOT NULL,
        false,
        true,
        NOW(),
        NOW()
    );

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 4. FIX RLS POLICIES FOR DOCTORS TABLE
-- =====================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Doctors can view own profile" ON doctors;
DROP POLICY IF EXISTS "Doctors can update own profile" ON doctors;
DROP POLICY IF EXISTS "Enable read access for all users" ON doctors;

-- Create proper policies
CREATE POLICY "Doctors can view own profile" ON doctors
    FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Doctors can update own profile" ON doctors
    FOR UPDATE USING (profile_id = auth.uid());

CREATE POLICY "Authenticated users can insert doctor profile" ON doctors
    FOR INSERT WITH CHECK (profile_id = auth.uid());

CREATE POLICY "Admins can view all doctors" ON doctors
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Service role can manage all doctors" ON doctors
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =====================================================
-- 5. FIX RLS POLICIES FOR PATIENTS TABLE
-- =====================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Patients can view own profile" ON patients;
DROP POLICY IF EXISTS "Patients can update own profile" ON patients;
DROP POLICY IF EXISTS "Enable read access for all users" ON patients;

-- Create proper policies
CREATE POLICY "Patients can view own profile" ON patients
    FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Patients can update own profile" ON patients
    FOR UPDATE USING (profile_id = auth.uid());

CREATE POLICY "Authenticated users can insert patient profile" ON patients
    FOR INSERT WITH CHECK (profile_id = auth.uid());

CREATE POLICY "Doctors can view their patients" ON patients
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('doctor', 'admin')
        )
    );

CREATE POLICY "Service role can manage all patients" ON patients
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =====================================================
-- 6. CREATE RPC FUNCTION FOR PROFILE CREATION
-- =====================================================

-- Drop existing function
DROP FUNCTION IF EXISTS create_user_profile(UUID, TEXT, TEXT, TEXT, TEXT);

-- Create RPC function for manual profile creation
CREATE OR REPLACE FUNCTION create_user_profile(
    user_id UUID,
    user_email TEXT,
    user_name TEXT,
    user_phone TEXT DEFAULT NULL,
    user_role TEXT DEFAULT 'patient'
)
RETURNS JSON AS $$
DECLARE
    new_profile profiles%ROWTYPE;
BEGIN
    -- Insert the profile
    INSERT INTO profiles (
        id,
        email,
        full_name,
        phone_number,
        role,
        email_verified,
        phone_verified,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        user_id,
        user_email,
        user_name,
        user_phone,
        user_role,
        false,
        false,
        true,
        NOW(),
        NOW()
    ) RETURNING * INTO new_profile;

    -- Return success response
    RETURN json_build_object(
        'success', true,
        'profile', row_to_json(new_profile)
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Return error response
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant permissions on profiles table
GRANT SELECT, INSERT, UPDATE ON profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON profiles TO anon;

-- Grant permissions on doctors table
GRANT SELECT, INSERT, UPDATE ON doctors TO authenticated;

-- Grant permissions on patients table
GRANT SELECT, INSERT, UPDATE ON patients TO authenticated;

-- Grant execute permission on the RPC function
GRANT EXECUTE ON FUNCTION create_user_profile(UUID, TEXT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_user_profile(UUID, TEXT, TEXT, TEXT, TEXT) TO anon;

-- =====================================================
-- 8. UPDATE EXISTING USERS (IF ANY)
-- =====================================================

-- Update any existing users that might not have profiles
DO $$
DECLARE
    auth_user RECORD;
BEGIN
    -- Loop through auth users that don't have profiles
    FOR auth_user IN 
        SELECT au.id, au.email, au.raw_user_meta_data
        FROM auth.users au
        LEFT JOIN profiles p ON au.id = p.id
        WHERE p.id IS NULL
    LOOP
        -- Create profile for this user
        INSERT INTO profiles (
            id,
            email,
            full_name,
            phone_number,
            role,
            email_verified,
            phone_verified,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            auth_user.id,
            auth_user.email,
            COALESCE(auth_user.raw_user_meta_data->>'full_name', split_part(auth_user.email, '@', 1)),
            auth_user.raw_user_meta_data->>'phone_number',
            COALESCE(auth_user.raw_user_meta_data->>'role', 'patient'),
            false,
            false,
            true,
            NOW(),
            NOW()
        ) ON CONFLICT (id) DO NOTHING;
        
        RAISE NOTICE 'Created profile for user: %', auth_user.email;
    END LOOP;
END $$;

-- =====================================================
-- 9. VERIFY SETUP
-- =====================================================

-- Check if trigger exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'on_auth_user_created'
    ) THEN
        RAISE NOTICE '✅ Trigger on_auth_user_created exists';
    ELSE
        RAISE NOTICE '❌ Trigger on_auth_user_created does not exist';
    END IF;
END $$;

-- Check if RPC function exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_proc 
        WHERE proname = 'create_user_profile'
    ) THEN
        RAISE NOTICE '✅ Function create_user_profile exists';
    ELSE
        RAISE NOTICE '❌ Function create_user_profile does not exist';
    END IF;
END $$;

-- Check profiles table policies
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = 'profiles';
    
    RAISE NOTICE '✅ Profiles table has % RLS policies', policy_count;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 AUTHENTICATION FIXES COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Fixed RLS policies for profiles, doctors, patients';
    RAISE NOTICE '✅ Created profile creation trigger';
    RAISE NOTICE '✅ Created RPC function for manual profile creation';
    RAISE NOTICE '✅ Updated existing users without profiles';
    RAISE NOTICE '✅ Granted necessary permissions';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 Please test authentication again!';
END $$;
