// =====================================================
// DYNAMIC ENUM SERVICE
// =====================================================

import { supabaseClient } from '@/lib/supabase-client';
import {
  EnumCategory,
  SystemEnum,
  SystemEnumWithCategory,
  EnumOption,
  Language,
  EnumCategoryType,
  CreateEnumRequest,
  UpdateEnumRequest,
  CreateEnumCategoryRequest,
  UpdateEnumCategoryRequest,
  EnumFilter,
  EnumSort,
  EnumCache,
  EnumValidationResult,
  BulkEnumOperation,
  BulkEnumResult,
} from '@/lib/types/enum.types';

class EnumService {
  private cache: EnumCache = {};
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // =====================================================
  // CACHE MANAGEMENT
  // =====================================================

  private isCacheValid(categoryId: string): boolean {
    const cached = this.cache[categoryId];
    return cached && Date.now() < cached.expiresAt;
  }

  private setCacheData(categoryId: string, enums: SystemEnum[]): void {
    this.cache[categoryId] = {
      enums,
      lastUpdated: Date.now(),
      expiresAt: Date.now() + this.CACHE_DURATION,
    };
  }

  private getCachedData(categoryId: string): SystemEnum[] | null {
    if (this.isCacheValid(categoryId)) {
      return this.cache[categoryId].enums;
    }
    return null;
  }

  public clearCache(categoryId?: string): void {
    if (categoryId) {
      delete this.cache[categoryId];
    } else {
      this.cache = {};
    }
  }

  // =====================================================
  // ENUM CATEGORIES
  // =====================================================

  async getEnumCategories(): Promise<EnumCategory[]> {
    try {
      const { data, error } = await supabaseClient
        .from('v_enum_categories')
        .select('*')
        .order('category_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching enum categories:', error);
      throw error;
    }
  }

  async createEnumCategory(request: CreateEnumCategoryRequest): Promise<EnumCategory> {
    try {
      const { data, error } = await supabaseClient
        .from('enum_categories')
        .insert([request])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating enum category:', error);
      throw error;
    }
  }

  async updateEnumCategory(request: UpdateEnumCategoryRequest): Promise<EnumCategory> {
    try {
      const { category_id, ...updateData } = request;
      const { data, error } = await supabaseClient
        .from('enum_categories')
        .update(updateData)
        .eq('category_id', category_id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating enum category:', error);
      throw error;
    }
  }

  async deleteEnumCategory(categoryId: string): Promise<void> {
    try {
      const { error } = await supabaseClient
        .from('enum_categories')
        .delete()
        .eq('category_id', categoryId)
        .eq('is_system', false); // Only allow deletion of non-system categories

      if (error) throw error;
      this.clearCache(categoryId);
    } catch (error) {
      console.error('Error deleting enum category:', error);
      throw error;
    }
  }

  // =====================================================
  // SYSTEM ENUMS
  // =====================================================

  async getEnumsByCategory(categoryId: string, useCache = true): Promise<SystemEnum[]> {
    // Check cache first
    if (useCache) {
      const cached = this.getCachedData(categoryId);
      if (cached) return cached;
    }

    try {
      const { data, error } = await supabaseClient
        .from('system_enums')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('sort_order')
        .order('display_name_vi');

      if (error) throw error;

      const enums = data || [];
      this.setCacheData(categoryId, enums);
      return enums;
    } catch (error) {
      console.error('Error fetching enums by category:', error);
      throw error;
    }
  }

  async getAllEnums(filter?: EnumFilter, sort?: EnumSort): Promise<SystemEnumWithCategory[]> {
    try {
      let query = supabaseClient
        .from('v_system_enums')
        .select('*');

      // Apply filters
      if (filter) {
        if (filter.categoryId) {
          query = query.eq('category_id', filter.categoryId);
        }
        if (filter.isActive !== undefined) {
          query = query.eq('is_active', filter.isActive);
        }
        if (filter.isSystem !== undefined) {
          query = query.eq('is_system', filter.isSystem);
        }
        if (filter.search) {
          const searchTerm = `%${filter.search}%`;
          query = query.or(`display_name_en.ilike.${searchTerm},display_name_vi.ilike.${searchTerm},enum_key.ilike.${searchTerm}`);
        }
      }

      // Apply sorting
      if (sort) {
        query = query.order(sort.field, { ascending: sort.direction === 'asc' });
      } else {
        query = query.order('category_name').order('sort_order');
      }

      const { data, error } = await query;
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all enums:', error);
      throw error;
    }
  }

  async createEnum(request: CreateEnumRequest): Promise<SystemEnum> {
    try {
      const { data, error } = await supabaseClient
        .from('system_enums')
        .insert([request])
        .select()
        .single();

      if (error) throw error;

      // Clear cache for this category
      this.clearCache(request.category_id);
      return data;
    } catch (error) {
      console.error('Error creating enum:', error);
      throw error;
    }
  }

  async updateEnum(request: UpdateEnumRequest): Promise<SystemEnum> {
    try {
      const { enum_id, ...updateData } = request;
      const { data, error } = await supabaseClient
        .from('system_enums')
        .update(updateData)
        .eq('enum_id', enum_id)
        .select()
        .single();

      if (error) throw error;

      // Clear cache for this category
      if (data.category_id) {
        this.clearCache(data.category_id);
      }
      return data;
    } catch (error) {
      console.error('Error updating enum:', error);
      throw error;
    }
  }

  async deleteEnum(enumId: string): Promise<void> {
    try {
      // Get the enum first to clear cache
      const { data: enumData } = await supabaseClient
        .from('system_enums')
        .select('category_id')
        .eq('enum_id', enumId)
        .single();

      const { error } = await supabaseClient
        .from('system_enums')
        .delete()
        .eq('enum_id', enumId)
        .eq('is_system', false); // Only allow deletion of non-system enums

      if (error) throw error;

      // Clear cache
      if (enumData?.category_id) {
        this.clearCache(enumData.category_id);
      }
    } catch (error) {
      console.error('Error deleting enum:', error);
      throw error;
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  async getEnumOptions(categoryId: string, language: Language = 'vi'): Promise<EnumOption[]> {
    const enums = await this.getEnumsByCategory(categoryId);

    return enums.map(enumItem => ({
      value: enumItem.enum_key,
      label: language === 'en' ? enumItem.display_name_en : enumItem.display_name_vi,
      description: language === 'en' ? enumItem.description_en : enumItem.description_vi,
      color: enumItem.color_code,
      icon: enumItem.icon_name,
      isDefault: enumItem.is_default,
    }));
  }

  // =====================================================
  // SPECIALIZED ENUM GETTERS
  // =====================================================

  async getSpecialties(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('SPECIALTIES', language);
  }

  async getDepartments(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('DEPARTMENTS', language);
  }

  async getRoomTypes(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('ROOM_TYPES', language);
  }

  async getDiagnosis(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('DIAGNOSIS', language);
  }

  async getMedications(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('MEDICATIONS', language);
  }

  async getStatusValues(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('STATUS_VALUES', language);
  }

  async getPaymentMethods(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('PAYMENT_METHODS', language);
  }

  async getUserRoles(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('USER_ROLES', language);
  }

  async getGenderOptions(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('GENDER', language);
  }

  async getBloodTypes(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('BLOOD_TYPE', language);
  }

  async getAppointmentTypes(language: Language = 'vi'): Promise<EnumOption[]> {
    return this.getEnumOptions('APPOINTMENT_TYPE', language);
  }

  async getEnumDisplayName(categoryId: string, enumKey: string, language: Language = 'vi'): Promise<string> {
    try {
      const { data, error } = await supabaseClient
        .rpc('get_enum_display_name', {
          p_category_id: categoryId,
          p_enum_key: enumKey,
          p_language: language,
        });

      if (error) throw error;
      return data || enumKey;
    } catch (error) {
      console.error('Error getting enum display name:', error);
      return enumKey; // Fallback to key if error
    }
  }

  async getDefaultEnum(categoryId: string): Promise<SystemEnum | null> {
    try {
      const { data, error } = await supabaseClient
        .from('system_enums')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_default', true)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return data || null;
    } catch (error) {
      console.error('Error getting default enum:', error);
      return null;
    }
  }

  async validateEnum(categoryId: string, enumKey: string): Promise<boolean> {
    try {
      const { data, error } = await supabaseClient
        .rpc('validate_enum_value', {
          p_category_id: categoryId,
          p_enum_key: enumKey,
        });

      if (error) throw error;
      return data || false;
    } catch (error) {
      console.error('Error validating enum:', error);
      return false;
    }
  }

  // =====================================================
  // BULK OPERATIONS
  // =====================================================

  async bulkUpdateEnums(operation: BulkEnumOperation): Promise<BulkEnumResult> {
    const result: BulkEnumResult = {
      success: true,
      processed: 0,
      failed: 0,
      errors: [],
    };

    for (const enumId of operation.enumIds) {
      try {
        switch (operation.operation) {
          case 'activate':
            await this.updateEnum({ enum_id: enumId, is_active: true });
            break;
          case 'deactivate':
            await this.updateEnum({ enum_id: enumId, is_active: false });
            break;
          case 'delete':
            await this.deleteEnum(enumId);
            break;
          case 'update_category':
            if (operation.data?.category_id) {
              await this.updateEnum({ enum_id: enumId, category_id: operation.data.category_id });
            }
            break;
        }
        result.processed++;
      } catch (error) {
        result.failed++;
        result.errors.push({
          enumId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    result.success = result.failed === 0;
    this.clearCache(); // Clear all cache after bulk operations
    return result;
  }
}

// Export singleton instance
export const enumService = new EnumService();
