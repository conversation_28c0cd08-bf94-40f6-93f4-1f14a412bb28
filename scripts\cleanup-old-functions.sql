-- =====================================================
-- CLEANUP OLD FUNCTIONS AND PROBLEMATIC CODE
-- =====================================================
-- This script removes old functions that might cause conflicts

-- =====================================================
-- 1. DROP OLD ENUM VALIDATION FUNCTIONS
-- =====================================================

-- Drop validate_enum function and all its variants
DROP FUNCTION IF EXISTS validate_enum(TEXT, TEXT);
DROP FUNCTION IF EXISTS validate_enum(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS validate_enum(TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.validate_enum(TEXT, TEXT);
DROP FUNCTION IF EXISTS public.validate_enum(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.validate_enum(TEXT, TEXT, TEXT, TEXT);

-- Drop any other enum-related functions
DROP FUNCTION IF EXISTS get_enum_values(TEXT);
DROP FUNCTION IF EXISTS public.get_enum_values(TEXT);
DROP FUNCTION IF EXISTS check_enum_value(TEXT, TEXT);
DROP FUNCTION IF EXISTS public.check_enum_value(TEXT, TEXT);

-- =====================================================
-- 2. DROP OLD TRIGGER FUNCTIONS
-- =====================================================

-- Drop old profile creation functions
DROP FUNCTION IF EXISTS handle_new_user_signup();
DROP FUNCTION IF EXISTS public.handle_new_user_signup();
DROP FUNCTION IF EXISTS create_profile_for_new_user();
DROP FUNCTION IF EXISTS public.create_profile_for_new_user();

-- Drop old triggers
DROP TRIGGER IF EXISTS on_auth_user_created_old ON auth.users;
DROP TRIGGER IF EXISTS handle_new_user_trigger ON auth.users;
DROP TRIGGER IF EXISTS create_profile_trigger ON auth.users;

-- =====================================================
-- 3. DROP OLD RPC FUNCTIONS
-- =====================================================

-- Drop old RPC functions that might conflict
DROP FUNCTION IF EXISTS create_profile(UUID, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.create_profile(UUID, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS setup_user_profile(UUID, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.setup_user_profile(UUID, TEXT, TEXT, TEXT, TEXT);

-- =====================================================
-- 4. CLEAN UP OLD POLICIES
-- =====================================================

-- Drop any old or conflicting policies
DROP POLICY IF EXISTS "Enable read access for all users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON profiles;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON profiles;
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can modify own profile" ON profiles;

-- Drop old doctor policies
DROP POLICY IF EXISTS "Enable read access for all users" ON doctors;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON doctors;
DROP POLICY IF EXISTS "Enable update for users based on email" ON doctors;
DROP POLICY IF EXISTS "Doctors can read own data" ON doctors;
DROP POLICY IF EXISTS "Doctors can modify own data" ON doctors;

-- Drop old patient policies
DROP POLICY IF EXISTS "Enable read access for all users" ON patients;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON patients;
DROP POLICY IF EXISTS "Enable update for users based on email" ON patients;
DROP POLICY IF EXISTS "Patients can read own data" ON patients;
DROP POLICY IF EXISTS "Patients can modify own data" ON patients;

-- =====================================================
-- 5. REMOVE OLD CONSTRAINTS
-- =====================================================

-- Remove old enum check constraints that might conflict
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find and drop enum-related constraints
    FOR constraint_name IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conname LIKE '%enum%' OR conname LIKE '%validate%'
    LOOP
        EXECUTE 'ALTER TABLE ' || (
            SELECT schemaname||'.'||tablename 
            FROM pg_tables 
            WHERE schemaname = 'public'
        ) || ' DROP CONSTRAINT IF EXISTS ' || constraint_name;
    END LOOP;
EXCEPTION
    WHEN OTHERS THEN
        -- Ignore errors, just continue
        NULL;
END $$;

-- =====================================================
-- 6. CLEAN UP OLD TYPES
-- =====================================================

-- Drop old enum types if they exist
DROP TYPE IF EXISTS user_role CASCADE;
DROP TYPE IF EXISTS gender_type CASCADE;
DROP TYPE IF EXISTS appointment_status CASCADE;
DROP TYPE IF EXISTS payment_status CASCADE;

-- =====================================================
-- 7. VERIFICATION
-- =====================================================

-- Check for remaining problematic functions
DO $$
DECLARE
    func_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO func_count
    FROM pg_proc 
    WHERE proname LIKE '%validate_enum%' OR proname LIKE '%enum%';
    
    IF func_count > 0 THEN
        RAISE NOTICE '⚠️ Still have % enum-related functions', func_count;
    ELSE
        RAISE NOTICE '✅ All enum functions cleaned up';
    END IF;
END $$;

-- Check for remaining triggers
DO $$
DECLARE
    trigger_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO trigger_count
    FROM pg_trigger 
    WHERE tgname LIKE '%user%' AND tgname != 'on_auth_user_created';
    
    IF trigger_count > 0 THEN
        RAISE NOTICE '⚠️ Still have % old user triggers', trigger_count;
    ELSE
        RAISE NOTICE '✅ Old triggers cleaned up';
    END IF;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🧹 CLEANUP COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Removed old enum validation functions';
    RAISE NOTICE '✅ Removed old trigger functions';
    RAISE NOTICE '✅ Removed old RPC functions';
    RAISE NOTICE '✅ Cleaned up old policies';
    RAISE NOTICE '✅ Removed old constraints';
    RAISE NOTICE '✅ Dropped old enum types';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 Now run the fix-auth-issues.sql script!';
END $$;
