# Hướng dẫn xóa dữ liệu trong Supabase

## 📋 Tổng quan

Tài liệu này hướng dẫn cách xóa dữ liệu trong cơ sở dữ liệu Supabase của hệ thống quản lý bệnh viện, đặc biệt là dữ liệu hiển thị trong các VIEW `doctor_details` và `patient_details`.

## 🔍 Hiểu về cấu trúc dữ liệu

### Bảng thực tế (Tables)
- `profiles` - Thông tin người dùng cơ bản
- `doctors` - Thông tin chi tiết bác sĩ
- `patients` - Thông tin chi tiết bệnh nhân
- `appointments` - Cuộc hẹn khám
- `medical_records` - Hồ sơ y tế
- `departments` - Khoa/phòng ban
- `rooms` - Phòng bệnh viện

### Views (Không phải bảng thực tế)
- `doctor_details` - Hi<PERSON><PERSON> thị thông tin đầy đủ bác sĩ (JOIN từ doctors + profiles + departments)
- `patient_details` - Hiể<PERSON> thị thông tin đầy đủ bệnh nhân (JOIN từ patients + profiles)
- `appointment_details` - Hiển thị thông tin đầy đủ cuộc hẹn

## 🛠️ Cách xóa dữ liệu

### Phương pháp 1: Sử dụng SQL Script (Khuyến nghị)

1. **Mở Supabase Dashboard**
2. **Vào SQL Editor**
3. **Chạy script từ file `scripts/delete-data-supabase.sql`**

#### Xóa dữ liệu doctor_details:
```sql
-- Xóa dữ liệu bác sĩ (sẽ làm trống doctor_details view)
DELETE FROM doctors;
DELETE FROM profiles WHERE role = 'doctor';
```

#### Xóa dữ liệu patient_details:
```sql
-- Xóa dữ liệu bệnh nhân (sẽ làm trống patient_details view)
DELETE FROM patients;
DELETE FROM profiles WHERE role = 'patient';
```

#### Xóa tất cả dữ liệu (theo thứ tự an toàn):
```sql
DELETE FROM medical_records;
DELETE FROM prescriptions;
DELETE FROM appointments;
DELETE FROM patients;
DELETE FROM doctors;
DELETE FROM admins;
DELETE FROM rooms;
DELETE FROM departments;
DELETE FROM profiles;
```

### Phương pháp 2: Sử dụng Node.js Script

```bash
# Chạy từ thư mục root của project
node backend/scripts/delete-supabase-data.js
```

**Menu lựa chọn:**
1. Xóa TẤT CẢ dữ liệu (tất cả bảng)
2. Xóa chỉ bảng PROFILES
3. Xóa dữ liệu DOCTOR_DETAILS (VIEW)
4. Xóa dữ liệu PATIENT_DETAILS (VIEW)
5. Xóa bảng cụ thể
6. Hủy bỏ

### Phương pháp 3: Xóa Auth Users (Supabase Authentication)

```bash
# Script chuyên dụng để xóa users trong auth.users
node backend/scripts/delete-auth-users.js
```

**Menu lựa chọn:**
1. Xem danh sách tất cả auth users
2. Xóa TẤT CẢ auth users
3. Xóa auth user cụ thể (chọn từ danh sách)
4. Xóa auth user theo email
5. Xóa TẤT CẢ auth users VÀ profiles (NGUY HIỂM!)
6. Hủy bỏ

## ⚠️ Lưu ý quan trọng

### 1. Backup dữ liệu trước khi xóa
```bash
# Xuất dữ liệu từ Supabase Dashboard
# Hoặc sử dụng pg_dump nếu có quyền truy cập
```

### 2. Row Level Security (RLS)
- Các bảng có RLS enabled cần quyền admin để xóa
- Có thể cần tạm thời disable RLS:
```sql
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
-- Thực hiện xóa dữ liệu
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
```

### 3. Foreign Key Constraints
- Xóa theo thứ tự từ bảng con đến bảng cha
- Hoặc tạm thời disable constraints

### 4. Supabase Auth (auth.users)
- Xóa `profiles` sẽ KHÔNG xóa users trong `auth.users`
- Để xóa auth users, PHẢI sử dụng Supabase Admin API
- Cần `SUPABASE_SERVICE_ROLE_KEY` để có quyền admin
- Không thể xóa auth.users bằng SQL thông thường

#### Cách xóa auth users:
```bash
# Sử dụng script chuyên dụng
node backend/scripts/delete-auth-users.js
```

#### Hoặc sử dụng code:
```javascript
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(url, serviceRoleKey);

// Xóa user cụ thể
await supabase.auth.admin.deleteUser(userId);

// Lấy danh sách tất cả users
const { data } = await supabase.auth.admin.listUsers();
```

### 5. Thứ tự xóa an toàn

**Để xóa TOÀN BỘ dữ liệu một cách an toàn:**

1. **Xóa dữ liệu phụ thuộc:**
   ```sql
   DELETE FROM medical_records;
   DELETE FROM prescriptions;
   DELETE FROM appointments;
   ```

2. **Xóa dữ liệu chính:**
   ```sql
   DELETE FROM patients;
   DELETE FROM doctors;
   DELETE FROM admins;
   DELETE FROM rooms;
   DELETE FROM departments;
   ```

3. **Xóa profiles:**
   ```sql
   DELETE FROM profiles;
   ```

4. **Xóa auth users (bằng script):**
   ```bash
   node backend/scripts/delete-auth-users.js
   ```

## 🔄 Khôi phục dữ liệu

Sau khi xóa, có thể khôi phục bằng cách:

1. **Chạy lại script tạo sample data:**
```bash
# Chạy trong Supabase SQL Editor
-- File: backend/scripts/insert-sample-data.sql
```

2. **Import từ backup đã tạo trước đó**

3. **Sử dụng script tạo tài khoản mới:**
```bash
node backend/scripts/create-sample-accounts.js
```

## 📊 Kiểm tra kết quả

Sau khi xóa, kiểm tra bằng SQL:

```sql
-- Kiểm tra số lượng records còn lại
SELECT 'profiles' as table_name, COUNT(*) as remaining_records FROM profiles
UNION ALL
SELECT 'doctors', COUNT(*) FROM doctors
UNION ALL
SELECT 'patients', COUNT(*) FROM patients
UNION ALL
SELECT 'doctor_details (VIEW)', COUNT(*) FROM doctor_details
UNION ALL
SELECT 'patient_details (VIEW)', COUNT(*) FROM patient_details;
```

## 🚨 Cảnh báo

- **KHÔNG THỂ HOÀN TÁC** sau khi xóa dữ liệu
- **LUÔN BACKUP** trước khi thực hiện
- **KIỂM TRA KỸ** trước khi confirm xóa
- **XÓA THEO THỨ TỰ** để tránh lỗi foreign key

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong console
2. Kiểm tra quyền truy cập Supabase
3. Kiểm tra RLS policies
4. Liên hệ admin hệ thống
