<!DOCTYPE html>
<html>
<head>
    <title>Simple Login Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Simple Login Test</h1>
    
    <div>
        <input type="email" id="email" placeholder="Email" value="">
        <input type="password" id="password" placeholder="Password" value="">
        <button onclick="testLogin()">Test Login</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        // Replace with your actual Supabase credentials
        const supabaseUrl = 'YOUR_SUPABASE_URL'
        const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey)
        
        async function testLogin() {
            const email = document.getElementById('email').value
            const password = document.getElementById('password').value
            const resultDiv = document.getElementById('result')
            
            try {
                console.log('🔄 Attempting login...')
                resultDiv.innerHTML = '🔄 Attempting login...'
                
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                })
                
                if (error) {
                    console.error('❌ Login error:', error)
                    resultDiv.innerHTML = `❌ Login failed: ${error.message}<br>Code: ${error.status}<br>Details: ${JSON.stringify(error, null, 2)}`
                    return
                }
                
                console.log('✅ Login successful:', data)
                resultDiv.innerHTML = `✅ Login successful!<br>User ID: ${data.user?.id}<br>Email: ${data.user?.email}`
                
                // Test profile access
                console.log('🔄 Testing profile access...')
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', data.user.id)
                    .single()
                
                if (profileError) {
                    console.error('❌ Profile access error:', profileError)
                    resultDiv.innerHTML += `<br>❌ Profile access failed: ${profileError.message}`
                } else {
                    console.log('✅ Profile access successful:', profile)
                    resultDiv.innerHTML += `<br>✅ Profile access successful!<br>Name: ${profile.full_name}<br>Role: ${profile.role}`
                }
                
            } catch (err) {
                console.error('❌ Unexpected error:', err)
                resultDiv.innerHTML = `❌ Unexpected error: ${err.message}`
            }
        }
        
        // Auto-fill with test credentials if available
        document.addEventListener('DOMContentLoaded', function() {
            // You can set test credentials here
            // document.getElementById('email').value = '<EMAIL>'
            // document.getElementById('password').value = 'TestPassword123!'
        })
    </script>
</body>
</html>
