-- =====================================================
-- SCRIPT XÓA DỮ LIỆU TRONG SUPABASE
-- =====================================================
-- Chạy script này trong Supabase SQL Editor để xóa dữ liệu

-- =====================================================
-- CẢNH BÁO: SCRIPT NÀY SẼ XÓA TẤT CẢ DỮ LIỆU
-- Hãy chắc chắn bạn muốn xóa dữ liệu trước khi chạy
-- =====================================================

-- =====================================================
-- PHƯƠNG PHÁP 1: XÓA DỮ LIỆU HIỂN THỊ TRONG VIEWS
-- =====================================================

-- LƯU Ý: doctor_details và patient_details là VIEWs, không phải bảng thực tế
-- Để xóa dữ liệu hiển thị trong các VIEW này, cần xóa từ bảng gốc

-- Xóa dữ liệu bác sĩ (sẽ làm trống doctor_details view)
DELETE FROM doctors;
DELETE FROM profiles WHERE role = 'doctor';

-- Xóa dữ liệu bệnh nhân (sẽ làm trống patient_details view)
DELETE FROM patients;
DELETE FROM profiles WHERE role = 'patient';

-- Hoặc xóa tất cả profiles (sẽ làm trống cả 2 views)
-- DELETE FROM profiles;

-- =====================================================
-- PHƯƠNG PHÁP 2: XÓA TẤT CẢ DỮ LIỆU THEO THỨ TỰ
-- (Xóa theo thứ tự để tránh lỗi foreign key constraint)
-- =====================================================

-- Bước 1: Xóa dữ liệu từ các bảng phụ thuộc trước
DELETE FROM medical_records;
DELETE FROM prescriptions;
DELETE FROM appointments;
DELETE FROM patients;
DELETE FROM doctors;
DELETE FROM admins;
DELETE FROM rooms;
DELETE FROM departments;

-- Bước 2: Xóa dữ liệu từ bảng profiles cuối cùng
DELETE FROM profiles;

-- =====================================================
-- PHƯƠNG PHÁP 3: XÓA DỮ LIỆU CÓ ĐIỀU KIỆN
-- =====================================================

-- Xóa chỉ profiles có role cụ thể
-- DELETE FROM profiles WHERE role = 'patient';
-- DELETE FROM profiles WHERE role = 'doctor';
-- DELETE FROM profiles WHERE role = 'admin';

-- Xóa profiles không hoạt động
-- DELETE FROM profiles WHERE is_active = false;

-- Xóa profiles được tạo trong khoảng thời gian cụ thể
-- DELETE FROM profiles WHERE created_at >= '2024-01-01' AND created_at < '2024-02-01';

-- =====================================================
-- PHƯƠNG PHÁP 4: XÓA VÀ RESET AUTO INCREMENT
-- =====================================================

-- Nếu bạn muốn reset lại ID sequence (không áp dụng cho UUID)
-- ALTER SEQUENCE profiles_id_seq RESTART WITH 1;

-- =====================================================
-- KIỂM TRA KẾT QUẢ SAU KHI XÓA
-- =====================================================

-- Kiểm tra số lượng records còn lại
SELECT 'profiles' as table_name, COUNT(*) as remaining_records FROM profiles
UNION ALL
SELECT 'doctors', COUNT(*) FROM doctors
UNION ALL
SELECT 'patients', COUNT(*) FROM patients
UNION ALL
SELECT 'appointments', COUNT(*) FROM appointments
UNION ALL
SELECT 'medical_records', COUNT(*) FROM medical_records
UNION ALL
SELECT 'departments', COUNT(*) FROM departments
UNION ALL
SELECT 'rooms', COUNT(*) FROM rooms;

-- =====================================================
-- PHƯƠNG PHÁP 5: XÓA AUTH USERS (SUPABASE AUTHENTICATION)
-- =====================================================

-- LƯU Ý: Không thể xóa auth.users bằng SQL thông thường
-- Cần sử dụng Supabase Admin API hoặc Node.js script

-- Để xóa auth users, sử dụng script Node.js:
-- node backend/scripts/delete-auth-users.js

-- Hoặc sử dụng Supabase Admin API trong code:
/*
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(url, serviceRoleKey);

// Xóa user cụ thể
await supabase.auth.admin.deleteUser(userId);

// Lấy danh sách tất cả users
const { data } = await supabase.auth.admin.listUsers();
*/

-- =====================================================
-- LƯU Ý QUAN TRỌNG
-- =====================================================
/*
1. BACKUP DỮ LIỆU TRƯỚC KHI XÓA:
   - Xuất dữ liệu ra file CSV từ Supabase Dashboard
   - Hoặc sử dụng pg_dump nếu có quyền truy cập database

2. ROW LEVEL SECURITY (RLS):
   - Nếu bảng có RLS enabled, bạn cần quyền admin để xóa
   - Có thể cần tạm thời disable RLS: ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

3. FOREIGN KEY CONSTRAINTS:
   - Xóa theo thứ tự từ bảng con đến bảng cha
   - Hoặc tạm thời disable constraints

4. SUPABASE AUTH (auth.users):
   - Xóa profiles sẽ KHÔNG xóa users trong auth.users
   - Để xóa auth users, PHẢI sử dụng Supabase Admin API
   - Cần SUPABASE_SERVICE_ROLE_KEY để có quyền admin
   - Sử dụng script: node backend/scripts/delete-auth-users.js

5. THỨ TỰ XÓA AN TOÀN:
   a) Xóa dữ liệu phụ thuộc (medical_records, appointments, etc.)
   b) Xóa profiles
   c) Xóa auth.users (bằng Admin API)

6. KHÔI PHỤC DỮ LIỆU:
   - Sau khi xóa, có thể chạy lại script tạo sample data
   - Hoặc import từ backup đã tạo trước đó
   - Lưu ý: auth.users cần tạo lại bằng signup process
*/
