const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase credentials');
    console.log('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Expected dynamic tables based on your system
const EXPECTED_DYNAMIC_TABLES = [
    'specialties',
    'departments_enum',
    'room_types',
    'diagnosis',
    'medications',
    'status_values',
    'payment_methods'
];

// Core tables
const CORE_TABLES = [
    'profiles',
    'doctors',
    'patients',
    'appointments',
    'departments',
    'rooms',
    'medical_records',
    'prescriptions',
    'billing',
    'payments',
    'schedules',
    'availability',
    'admins'
];

async function checkTableExists(tableName) {
    try {
        const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);

        if (error) {
            return { exists: false, error: error.message };
        }

        return { exists: true, error: null };
    } catch (err) {
        return { exists: false, error: err.message };
    }
}

async function getTableCount(tableName) {
    try {
        const { count, error } = await supabase
            .from(tableName)
            .select('*', { count: 'exact', head: true });

        if (error) {
            return { count: 0, error: error.message };
        }

        return { count: count || 0, error: null };
    } catch (err) {
        return { count: 0, error: err.message };
    }
}

async function checkDynamicTables() {
    console.log('\n🔍 CHECKING DYNAMIC TABLES STATUS\n');
    console.log('=' .repeat(60));

    const results = {
        existing: [],
        missing: [],
        errors: []
    };

    for (const tableName of EXPECTED_DYNAMIC_TABLES) {
        console.log(`\n📋 Checking table: ${tableName}`);

        const tableCheck = await checkTableExists(tableName);

        if (tableCheck.exists) {
            const countResult = await getTableCount(tableName);
            console.log(`   ✅ EXISTS - Records: ${countResult.count}`);
            results.existing.push({
                name: tableName,
                count: countResult.count,
                error: countResult.error
            });
        } else {
            console.log(`   ❌ MISSING - ${tableCheck.error}`);
            results.missing.push({
                name: tableName,
                error: tableCheck.error
            });
        }
    }

    return results;
}

async function checkCoreTables() {
    console.log('\n🏥 CHECKING CORE TABLES STATUS\n');
    console.log('=' .repeat(60));

    const results = {
        existing: [],
        missing: [],
        errors: []
    };

    for (const tableName of CORE_TABLES) {
        console.log(`\n📋 Checking table: ${tableName}`);

        const tableCheck = await checkTableExists(tableName);

        if (tableCheck.exists) {
            const countResult = await getTableCount(tableName);
            console.log(`   ✅ EXISTS - Records: ${countResult.count}`);
            results.existing.push({
                name: tableName,
                count: countResult.count,
                error: countResult.error
            });
        } else {
            console.log(`   ❌ MISSING - ${tableCheck.error}`);
            results.missing.push({
                name: tableName,
                error: tableCheck.error
            });
        }
    }

    return results;
}

async function getAllTables() {
    try {
        // Query to get all tables in public schema
        const { data, error } = await supabase.rpc('get_all_tables');

        if (error) {
            console.log('⚠️  Cannot use RPC to get tables, using manual check instead');
            return null;
        }

        return data;
    } catch (err) {
        console.log('⚠️  RPC not available, using manual table check');
        return null;
    }
}

async function generateSummaryReport(dynamicResults, coreResults) {
    console.log('\n📊 SUMMARY REPORT\n');
    console.log('=' .repeat(60));

    console.log('\n🔧 DYNAMIC TABLES (7 expected):');
    console.log(`   ✅ Existing: ${dynamicResults.existing.length}`);
    console.log(`   ❌ Missing: ${dynamicResults.missing.length}`);

    if (dynamicResults.existing.length > 0) {
        console.log('\n   📋 Existing Dynamic Tables:');
        dynamicResults.existing.forEach(table => {
            console.log(`      • ${table.name} (${table.count} records)`);
        });
    }

    if (dynamicResults.missing.length > 0) {
        console.log('\n   ❌ Missing Dynamic Tables:');
        dynamicResults.missing.forEach(table => {
            console.log(`      • ${table.name}`);
        });
    }

    console.log('\n🏥 CORE TABLES:');
    console.log(`   ✅ Existing: ${coreResults.existing.length}`);
    console.log(`   ❌ Missing: ${coreResults.missing.length}`);

    if (coreResults.missing.length > 0) {
        console.log('\n   ❌ Missing Core Tables:');
        coreResults.missing.forEach(table => {
            console.log(`      • ${table.name}`);
        });
    }

    // Status assessment
    console.log('\n🎯 STATUS ASSESSMENT:');

    if (dynamicResults.existing.length === EXPECTED_DYNAMIC_TABLES.length) {
        console.log('   ✅ All dynamic tables are present');
    } else if (dynamicResults.existing.length === 0) {
        console.log('   ❌ No dynamic tables found - using enum system');
    } else {
        console.log('   ⚠️  Partial dynamic table implementation');
    }

    if (coreResults.missing.length === 0) {
        console.log('   ✅ All core tables are present');
    } else {
        console.log(`   ⚠️  ${coreResults.missing.length} core tables missing`);
    }

    console.log('\n💡 RECOMMENDATIONS:');

    if (dynamicResults.missing.length > 0) {
        console.log('   📝 Run the fix-database-tables.sql script to create missing dynamic tables');
        console.log('   📁 File location: backend/scripts/fix-database-tables.sql');
    }

    if (coreResults.missing.length > 0) {
        console.log('   📝 Run the setup script to create missing core tables');
        console.log('   📁 File location: scripts/create-linked-sample-data.sql');
    }

    if (dynamicResults.existing.length === EXPECTED_DYNAMIC_TABLES.length) {
        console.log('   🎉 Your system is using the new dynamic table structure!');
    }
}

async function main() {
    console.log('🏥 HOSPITAL MANAGEMENT - DATABASE STATUS CHECK');
    console.log('=' .repeat(60));
    console.log(`📡 Supabase URL: ${supabaseUrl}`);
    console.log(`🔑 Service Key: ${supabaseServiceKey ? '***' + supabaseServiceKey.slice(-4) : 'Not set'}`);

    try {
        // Test connection first
        console.log('\n🔗 Testing Supabase connection...');
        const { data, error } = await supabase.from('profiles').select('id').limit(1);

        if (error) {
            console.log('❌ Connection failed:', error.message);
            process.exit(1);
        }

        console.log('✅ Connection successful');

        // Check dynamic tables
        const dynamicResults = await checkDynamicTables();

        // Check core tables
        const coreResults = await checkCoreTables();

        // Generate summary
        await generateSummaryReport(dynamicResults, coreResults);

        console.log('\n✅ Database status check completed!');

    } catch (error) {
        console.error('\n❌ Error during database check:', error.message);
        process.exit(1);
    }
}

// Run the check
main();
