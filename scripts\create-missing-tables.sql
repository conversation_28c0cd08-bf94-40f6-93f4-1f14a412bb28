-- =====================================================
-- CREATE MISSING TABLES FOR HOSPITAL MANAGEMENT SYSTEM
-- =====================================================
-- Tables to create: billing, payments, schedules, availability
-- This script creates the missing core tables with proper relationships

-- =====================================================
-- 1. BILLING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS billing (
    bill_id VARCHAR(20) PRIMARY KEY,
    patient_id VARCHAR(20) NOT NULL,
    appointment_id VARCHAR(20),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    bill_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMPTZ NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled', 'refunded')),
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    insurance_coverage DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES profiles(id),
    
    -- Foreign key constraints
    CONSTRAINT fk_billing_patient FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE,
    CONSTRAINT fk_billing_appointment FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL,
    
    -- Check constraints
    CONSTRAINT chk_billing_amounts CHECK (
        subtotal >= 0 AND 
        tax_amount >= 0 AND 
        discount_amount >= 0 AND 
        insurance_coverage >= 0 AND
        total_amount >= 0
    )
);

-- =====================================================
-- 2. PAYMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS payments (
    payment_id VARCHAR(20) PRIMARY KEY,
    bill_id VARCHAR(20) NOT NULL,
    payment_method_id INTEGER,
    amount DECIMAL(10,2) NOT NULL,
    payment_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    transaction_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    notes TEXT,
    processed_by UUID REFERENCES profiles(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_payments_bill FOREIGN KEY (bill_id) REFERENCES billing(bill_id) ON DELETE CASCADE,
    CONSTRAINT fk_payments_method FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id),
    
    -- Check constraints
    CONSTRAINT chk_payment_amount CHECK (amount > 0)
);

-- =====================================================
-- 3. SCHEDULES TABLE (Doctor Weekly Schedules)
-- =====================================================

CREATE TABLE IF NOT EXISTS schedules (
    schedule_id VARCHAR(20) PRIMARY KEY,
    doctor_id VARCHAR(20) NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0=Sunday, 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT true,
    break_start_time TIME,
    break_end_time TIME,
    max_appointments INTEGER DEFAULT 8,
    appointment_duration INTEGER DEFAULT 30, -- minutes
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES profiles(id),
    
    -- Foreign key constraints
    CONSTRAINT fk_schedules_doctor FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE,
    
    -- Check constraints
    CONSTRAINT chk_schedule_times CHECK (end_time > start_time),
    CONSTRAINT chk_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND break_end_time > break_start_time)
    ),
    
    -- Unique constraint: one schedule per doctor per day
    UNIQUE(doctor_id, day_of_week)
);

-- =====================================================
-- 4. AVAILABILITY TABLE (Doctor Specific Date Availability)
-- =====================================================

CREATE TABLE IF NOT EXISTS availability (
    availability_id VARCHAR(20) PRIMARY KEY,
    doctor_id VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    reason VARCHAR(255), -- Reason if not available (vacation, sick, etc.)
    max_appointments INTEGER,
    appointment_duration INTEGER DEFAULT 30, -- minutes
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES profiles(id),
    
    -- Foreign key constraints
    CONSTRAINT fk_availability_doctor FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE,
    
    -- Check constraints
    CONSTRAINT chk_availability_times CHECK (end_time > start_time),
    CONSTRAINT chk_availability_future CHECK (date >= CURRENT_DATE),
    
    -- Unique constraint: one availability record per doctor per date
    UNIQUE(doctor_id, date)
);

-- =====================================================
-- 5. BILL ITEMS TABLE (Supporting table for billing)
-- =====================================================

CREATE TABLE IF NOT EXISTS bill_items (
    item_id VARCHAR(20) PRIMARY KEY,
    bill_id VARCHAR(20) NOT NULL,
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('consultation', 'procedure', 'medication', 'lab_test', 'room_charge', 'other')),
    service_id VARCHAR(20), -- Reference to specific service
    description TEXT NOT NULL,
    quantity INTEGER DEFAULT 1 CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_bill_items_bill FOREIGN KEY (bill_id) REFERENCES billing(bill_id) ON DELETE CASCADE
);

-- =====================================================
-- 6. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Billing indexes
CREATE INDEX IF NOT EXISTS idx_billing_patient_id ON billing(patient_id);
CREATE INDEX IF NOT EXISTS idx_billing_appointment_id ON billing(appointment_id);
CREATE INDEX IF NOT EXISTS idx_billing_status ON billing(status);
CREATE INDEX IF NOT EXISTS idx_billing_bill_date ON billing(bill_date);
CREATE INDEX IF NOT EXISTS idx_billing_due_date ON billing(due_date);

-- Payments indexes
CREATE INDEX IF NOT EXISTS idx_payments_bill_id ON payments(bill_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_payment_date ON payments(payment_date);

-- Schedules indexes
CREATE INDEX IF NOT EXISTS idx_schedules_doctor_id ON schedules(doctor_id);
CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_active ON schedules(is_active);

-- Availability indexes
CREATE INDEX IF NOT EXISTS idx_availability_doctor_id ON availability(doctor_id);
CREATE INDEX IF NOT EXISTS idx_availability_date ON availability(date);
CREATE INDEX IF NOT EXISTS idx_availability_doctor_date ON availability(doctor_id, date);

-- Bill items indexes
CREATE INDEX IF NOT EXISTS idx_bill_items_bill_id ON bill_items(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_items_service_type ON bill_items(service_type);

-- =====================================================
-- 7. CREATE TRIGGERS FOR AUTO-UPDATE
-- =====================================================

-- Update timestamps trigger function (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at column
DROP TRIGGER IF EXISTS update_billing_updated_at ON billing;
CREATE TRIGGER update_billing_updated_at
    BEFORE UPDATE ON billing
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_schedules_updated_at ON schedules;
CREATE TRIGGER update_schedules_updated_at
    BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_availability_updated_at ON availability;
CREATE TRIGGER update_availability_updated_at
    BEFORE UPDATE ON availability
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. CREATE FUNCTIONS FOR ID GENERATION
-- =====================================================

-- Function to generate bill ID
CREATE OR REPLACE FUNCTION generate_bill_id()
RETURNS VARCHAR(20) AS $$
BEGIN
    RETURN 'BILL' || EXTRACT(EPOCH FROM NOW())::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- Function to generate payment ID
CREATE OR REPLACE FUNCTION generate_payment_id()
RETURNS VARCHAR(20) AS $$
BEGIN
    RETURN 'PAY' || EXTRACT(EPOCH FROM NOW())::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- Function to generate schedule ID
CREATE OR REPLACE FUNCTION generate_schedule_id()
RETURNS VARCHAR(20) AS $$
BEGIN
    RETURN 'SCH' || EXTRACT(EPOCH FROM NOW())::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- Function to generate availability ID
CREATE OR REPLACE FUNCTION generate_availability_id()
RETURNS VARCHAR(20) AS $$
BEGIN
    RETURN 'AVL' || EXTRACT(EPOCH FROM NOW())::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- Function to generate bill item ID
CREATE OR REPLACE FUNCTION generate_bill_item_id()
RETURNS VARCHAR(20) AS $$
BEGIN
    RETURN 'ITEM' || EXTRACT(EPOCH FROM NOW())::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. ENABLE ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE billing ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE bill_items ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 10. CREATE RLS POLICIES
-- =====================================================

-- Billing policies
CREATE POLICY "Users can view their own bills" ON billing
    FOR SELECT USING (
        patient_id IN (
            SELECT patient_id FROM patients 
            WHERE profile_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM doctors 
            WHERE profile_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins and doctors can manage bills" ON billing
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'doctor')
        )
    );

-- Payments policies
CREATE POLICY "Users can view payments for their bills" ON payments
    FOR SELECT USING (
        bill_id IN (
            SELECT bill_id FROM billing
            WHERE patient_id IN (
                SELECT patient_id FROM patients 
                WHERE profile_id = auth.uid()
            )
        ) OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'doctor')
        )
    );

CREATE POLICY "Admins can manage payments" ON payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Schedules policies
CREATE POLICY "Doctors can manage their own schedules" ON schedules
    FOR ALL USING (
        doctor_id IN (
            SELECT doctor_id FROM doctors 
            WHERE profile_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Everyone can view active schedules" ON schedules
    FOR SELECT USING (is_active = true);

-- Availability policies
CREATE POLICY "Doctors can manage their own availability" ON availability
    FOR ALL USING (
        doctor_id IN (
            SELECT doctor_id FROM doctors 
            WHERE profile_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Everyone can view doctor availability" ON availability
    FOR SELECT USING (true);

-- Bill items policies
CREATE POLICY "Users can view bill items for their bills" ON bill_items
    FOR SELECT USING (
        bill_id IN (
            SELECT bill_id FROM billing
            WHERE patient_id IN (
                SELECT patient_id FROM patients 
                WHERE profile_id = auth.uid()
            )
        ) OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'doctor')
        )
    );

CREATE POLICY "Admins and doctors can manage bill items" ON bill_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'doctor')
        )
    );

-- =====================================================
-- SCRIPT COMPLETED
-- =====================================================

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE '✅ Missing tables created successfully!';
    RAISE NOTICE '📋 Created tables: billing, payments, schedules, availability, bill_items';
    RAISE NOTICE '🔒 RLS policies applied for security';
    RAISE NOTICE '⚡ Indexes created for performance';
    RAISE NOTICE '🔄 Triggers added for auto-updates';
END $$;
